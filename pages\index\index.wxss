page{
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #FAF8F5;
}
.page{
  width: 100%;
  height: 100%;
}
.bg{
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 144rpx;
  z-index: -1;
}
.header{
  position: relative;
}
.line{
  margin-left: 17rpx;
  margin-right: 127rpx;
  height: 86rpx;
}
.search{
  background-color: white;
  border-radius: 42rpx;
  height: 86rpx;
}
.input{
  height: 86rpx;
  width: 324rpx;
  display: inline-block;
}
.iconStyle{
  width: 40rpx;
  height: 40rpx;
  float: left;
  margin-top: 24rpx;
  margin-left: 28rpx;
  margin-right: 8rpx;
}
.btn_search{
  width: 138rpx;
  height: 78rpx;
  background: #0C72FF !important;
  border-radius: 39rpx;
  font-size: 30rpx;
  color: #FFFFFF;
  float: right;
  margin-top: 4rpx;
  margin-right: 4rpx;
}
.iconStyle2{
  position: absolute;
  top: 13rpx;
  right: 32rpx;
  width: 60rpx;
  height: 60rpx;
}
.placeClass{
  font-size: 28rpx;
  color: #B0B3BE;
}
.no{
  position: absolute;
  bottom: -90rpx;
  left: 18rpx;
  width: 714rpx;
  height: 84rpx;
  line-height: 84rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 14rpx;
}
.iconStyle3{
  float: left;
  width: 30rpx;
  height: 30rpx;
  margin-top: 27rpx;
  margin-left: 17rpx;
  margin-right: 11rpx;
}
.iconStyle4{
  position: absolute;
  right: 280rpx;
  top: 14rpx;
  width: 30rpx;
  height: 30rpx;
  /* z-index: 99999999; */
  border: 16rpx solid #fff;
}
.waterCodeTitle{
  height: 84rpx;
  line-height: 84rpx;
  font-weight: 500;
  font-size: 28rpx;
  font-family: PingFang SC;
  color: #3A3A3A;
}
.waterCode{
  height: 84rpx;
  line-height: 84rpx;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 32rpx;
  color: #181818;
}
.body{
  width: 714rpx;
  height: 900rpx;
  background-color: #FFFFFF;
  border-radius: 14rpx;
  margin-top: 100rpx;
  margin-left: 18rpx;
  position: relative;
}
.title{
  border-bottom: 1px solid #DCDCDC;
  color: #3A3A3A;
}
.title_style1{
  width: 240rpx;
  display: inline-block;
  padding-left: 20rpx;
  height: 88rpx;
  line-height: 88rpx;
}
.title_style2{
  width: 200rpx;
  display: inline-block;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
}
.title_style3{
  width: 125rpx;
  display: inline-block;
  height: 88rpx;
  line-height: 88rpx;
}
.goods{
  height: 114rpx;
  border-bottom: 1px solid #DCDCDC;
}
.goodsName{
  display: inline-block;
  width: 240rpx;
  padding-left: 20rpx;
  line-height: 57rpx;
}
.goodsDetail{
  display: inline-block;
  width: 240rpx;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  line-height: 28rpx;
  height: 28rpx;
  font-size: 26rpx;
  color: #181818;
}
.goodsDetail2{
  font-size: 24rpx;
  color: #444444;
}
.goodsNum{
  width: 200rpx;
  display: inline-block;
  line-height: 114rpx;
  height: 114rpx;
  vertical-align: top;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  font-size: 26rpx;
  color: #181818;
}
.goodsNumInput{
  width: 100%;
  height: 114rpx;
  text-align: center;
}
.goodsPrice,.goodsSum{
  width: 125rpx;
  display: inline-block;
  line-height: 114rpx;
  height: 114rpx;
  vertical-align: top;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  font-size: 26rpx;
  color: #181818;
}
.btn_del{
  background-color: #0C72FF;
  height: 120rpx;
  line-height: 120rpx;
  width: 130rpx;
  text-align: center;
  color: #fff;
  font-size: 30rpx;
}
.btn_group{
  position: fixed;
  bottom: 160rpx;
  width: 100%;
  left: 0;
  display: flex;
  justify-content: space-around;
  border: 1px solid #F0F0F0;
  padding-top: 14rpx;
  padding-bottom: 14rpx;
  background-color: #fff;
}
.btn_style{
  display: inline-block;
  width: 188rpx;
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #FFFFFF;
  font-family: PingFang SC;
}
.btn_clearAll{
  background-color: #fff;
  border: 1px solid #DCDCDC;
  border-radius: 33rpx !important;
  height: 66rpx;
  width: 198rpx;
}
.img_empty{
  width: 380rpx;
  height: 380rpx;
}
.emptyText{
  color: #9F9F9F;
  font-size: 26rpx;
  margin-top: -40rpx;
}
.total{
  position: absolute;
  width: 100%;
  bottom: 20rpx;
  left: 0;
  background-color: #fff;
}
.totalCount{
  float: left;
  margin-left: 32rpx;
  font-size: 26rpx;
  color: #181818;
}
.goodsAllSum{
  float: right;
  margin-right: 32rpx;
  font-size: 26rpx;
  color: #181818;
}
.watermark{
  position: fixed;
  top: 10vh;
  left:-50vw;
  width: 200vw;
  height: 60vh;
  pointer-events: none;
  transform: rotate(-45deg);
}
.watermarkText{
  pointer-events: none;
  width: 400rpx;
  display: inline-block;
  font-size:70rpx;
  height: 400rpx;
  line-height: 400rpx;
  color:#999;
  opacity: 0.2;
}
.btn_cancelOrder{
  background-color: #B5B5B5 !important;
}
.btn_vip{
  background-color: #FF6022 !important;
}
.btn_pay{
  background-color: #0C72FF !important;
}
.goodsSingle{
  z-index: 2;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 90vh;
  background-color: #FAF8F5;
}
.formStyle1Body{
  width: 714rpx;
  height: 340rpx;
  background: #FFFFFF;
  border-radius: 14rpx;
  margin: 18rpx;
  padding-top: 18rpx;
}
.formStyle1Line{
  margin-bottom: 30rpx;
}
.formStyle1Title{
  display: inline-block;
  width: 158rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #3A3A3A;
  text-align: right;
  padding-right: 14rpx;
}
.formStyle1TitleInput{
  display: inline-block;
  vertical-align: middle;
  background: #EEEEEE;
  height: 80rpx;
  width: 494rpx;
  border-radius: 8rpx;
  border: 1px solid #DCDCDC;
  color: #9E9E9E;
  padding-left: 20rpx;
  font-size: 28rpx;
}
.formStyle1TitleInputPlaceholderStyle{
  font-size: 28rpx;
  color: #9E9E9E;
}
.formStyle1Button{
  background: #0C72FF !important;
  border-radius: 8rpx !important;
  width: 188rpx;
  height: 60rpx;
  line-height: 60rpx;
  color: #FFFFFF;
  font-size: 28rpx;
  margin-top: 46rpx;
}
/* 底部弹出 */
.popText{
  height: 88rpx;
  line-height: 88rpx;
  font-size: 30rpx;
  color: #262626;
  padding-left: 20rpx;
  border-bottom: 1px solid #DCDCDC;
}
.goods{
  height: 114rpx;
  border-bottom: 1px solid #DCDCDC;
}
.popGoodsName{
  display: inline-block;
  width: 300rpx;
  padding-left: 20rpx;
  line-height: 57rpx;
}
.popGoodsDetail{
  display: inline-block;
  width: 250rpx;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  line-height: 28rpx;
  height: 28rpx;
  font-size: 26rpx;
  color: #181818;
}
.popGoodsPrice{
  width: 230rpx;
  display: inline-block;
  /* line-height: 114rpx;
  height: 114rpx; */
  vertical-align: top;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  font-size: 26rpx;
  color: #181818;
}
.goodsDetail3{
  display: block;
  width: 200rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 57rpx;
  height: 57rpx;
  font-size: 26rpx;
  color: #181818;
}
.goodsDetail4{
  display: block;
  width: 200rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 57rpx;
  height: 57rpx;
  font-size: 26rpx;
  color: #181818;
}
.popChoose{
  width: 154rpx;
  display: inline-block;
  /* line-height: 114rpx; */
  /* height: 114rpx; */
  text-align: right;
}
.popBtn_choose{
  width: 138rpx;
  height: 78rpx;
  background: #0C72FF;
  border-radius: 39rpx;
  font-size: 30rpx;
  color: #FFFFFF;
}
/* 会员弹出层 */
.vipLine{
  margin-left: 30rpx;
  margin-right: 100rpx;
  height: 86rpx;
  margin-top: 74rpx;
}
.vipSearch{
  background-color: white;
  border-radius: 42rpx;
  height: 86rpx;
  border: 1px solid #0C72FF;
}
.vipInput{
  height: 86rpx;
  width: 290rpx;
  display: inline-block;
}
.vipIconStyle2{
  width: 60rpx;
  height: 60rpx;
  position: absolute;
  top: 90rpx;
  right: 20rpx;
}
.vipInfoLine{
  border-bottom: 1px solid #DCDCDC;
  height: 80rpx;
  line-height: 80rpx;
}
.vipInfo{
  font-size: 26rpx;
  color: #1C1C1C;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
}
.vipChoose{
  text-align: center;
  position: absolute;
  bottom: 30rpx;
  left: calc(50% - 94rpx );
}
.vipBtn_choose{
  width: 188rpx;
  height: 60rpx;
  line-height: 60rpx;
  background: #0C72FF;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #FFFFFF;
}