// pages/pay/result.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    status:'失败',
    message:'',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    var that=this;
    that.setData({
      message:options.msg,
      status:options.status
    })
    wx.showModal({
      title: '提示',
      content: options.msg,
      showCancel:false,
      complete: (res) => {
        if (res.cancel) {
          setTimeout(() => {
            wx.reLaunch({
              url: 'index',
            })
          }, 500);
        }
        if (res.confirm) {
          setTimeout(() => {
            wx.reLaunch({
              url: 'index',
            })
          }, 500);
        }
      }
    })
     
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    wx.hideHomeButton();  
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})