var app=getApp();
import {https} from '../../utils/service'
Page({
  /**
   * 页面的初始数据
   */
  data: {
    searchText:'', // 商品搜索
    vipSearchText:'', //vip搜索
    waterCode:'',
    active: 0,
    showGoodsListFlag:false,
    singleInputFlag:false,
    vipAddFlag:false,
    totalPrice:0,
    editGoods:{
      c_adno: "0403",
      c_basic_unit: "件",
      c_gcode: "10500392",
      c_name: "18K珍珠饰品",
      c_price: 2900,
      c_pro_status: "普通商品",
      c_subcode: "001",
      c_type: "专柜单品",
      c_in_code:'',
      c_qtty:'',
      c_amount:'',
      price_readonly:1, //1只读
      qtty_readonly:1
    },
    getGoodsList:[], //搜索到的产品列表
    goodsList:[],
    vipList:[],// 会员搜索列表
    vipInfo:null,// 会员信息
    storeInfo:{
      store_id:"",
      store_name:""
    },// 商店信息
  },
  scanCode(){
    var that=this;
    wx.scanCode({
      onlyFromCamera: true,
      success (res) {
        that.setData({
          searchText:res.result
        })
        that.btn_search();
      }
    })
  },
  // 会员扫码
  scanCodeVip(){
    var that=this;
    wx.scanCode({
      onlyFromCamera: true,
      scanType:['qrCode'],
      success (res) {
        console.log(res)
        that.setData({
          vipSearchText:res.result
        })
        that.vipBtn_search();
      }
    })
  },
  btn_clean(){
    var that=this;
    that.setData({
      searchText:''
    })
  },
  // tabbar
  onChange(e) {
    console.log(e)
    var that=this;
    let index=e.detail;
    if(index==1){
      wx.reLaunch({
        url: '../waitingSettlement/index',
      })
    }
    if(index==2){
      if(that.data.goodsList.length>0){
        wx.showModal({
          title: '提示',
          content: '商品尚未结算，确认离开该页面？',
          success (res) {
            if (res.confirm) {
              wx.reLaunch({
                url: '../mine/index',
              })
            } else if (res.cancel) {
              // console.log('用户点击取消')
            }
          }
        })
      }else{
        wx.reLaunch({
          url: '../mine/index',
        })
      }
    }
  },
  onClose() {
    this.setData({
       showGoodsListFlag: false,
       vipAddFlag:false 
    });
  },
  getWaterCode(){
    var that=this;
    app.validateAndGetWaterCode((waterCode) => {
      if(waterCode) {
        that.setData({
          waterCode: waterCode
        });
        console.log("流水号获取成功:", waterCode);
      } else {
        console.error("流水号获取失败");
      }
    });
  },
  btn_del(e){
    var that=this;
    let index=e.currentTarget.dataset.index;
    console.log(index)
    wx.showModal({
      title: '提示',
      content: '确定删除商品吗？',
      success (res) {
        if (res.confirm) {
          // console.log('用户点击确定')
          that.data.goodsList.splice(index, 1);
          that.setData({
            goodsList:that.data.goodsList
          })
          that.calcPrice()
        } else if (res.cancel) {
          // console.log('用户点击取消')
        }
      }
    })
  },
  popBtn_choose(e){
    var that=this;
    let index=e.currentTarget.dataset.index;
    console.log(index)
    let t_goods=that.data.getGoodsList[index];
    if(t_goods.c_qtty==null||t_goods.c_qtty==undefined){// 无默认数量
      t_goods.c_nowNumber=1;
    }else{
      t_goods.c_nowNumber=t_goods.c_qtty;
    }
    t_goods.c_nowPrice=t_goods.c_price;
    if(t_goods.c_amount==null||t_goods.c_amount==undefined){// 散装无初始金额
      t_goods.c_nowSum=t_goods.c_nowNumber*t_goods.c_nowPrice;
    }else{
      t_goods.c_nowSum=t_goods.c_amount;
    }
    that.setData({
      showGoodsListFlag: false,
      singleInputFlag:true,
      editGoods:t_goods
    })
  },
  btn_clearAll(){
    var that=this;
    that.setData({
      goodsList:[]
    })
  },
  input_search(e){
    let val=e.detail.value;
    this.setData({
      searchText:val
    })
  },
  goodsNumInput(e){
    var that=this;
    let val=e.detail.value;
    let index=e.currentTarget.dataset.index;
    // console.log(val)
    // console.log(that.data.goodsList)
    that.data.goodsList[index].c_nowNumber=val;
    that.setData({
      goodsList:that.data.goodsList
    })
    that.calcPrice()
  },
  vipInput_search(e){
    let val=e.detail.value;
    this.setData({
      vipSearchText:val
    })
  },
  input_price(e){
    let val=e.detail.value;
    var that=this;
    that.data.editGoods.c_nowPrice=val;
  },
  saveGoods(){
    var that=this;
    that.data.editGoods.c_nowSum=that.data.editGoods.c_nowNumber*that.data.editGoods.c_nowPrice;
    that.data.goodsList.push(that.data.editGoods);
    that.setData({
      goodsList: that.data.goodsList,
      singleInputFlag: false
    })
    that.calcPrice();
  },
  // 商品搜索
  btn_search(){
    var that=this;
    if(that.data.waterCode==''){
      wx.showModal({
        title: '提示',
        content: '正在重新获取流水号',
        showCancel:false,
        complete: (res) => {
          if (res.cancel) {
            that.getWaterCode()
          }
          if (res.confirm) {
            that.getWaterCode()
          }
        }
      })
      return false
    }
    console.log(that.data.searchText);
    if(that.data.searchText==''){
      wx.showToast({
        title: '输入为空',
        icon:'error'
      })
      return false
    }
    let params={
      data:{
        "type": "gds",
        data:{
          "gcode":that.data.searchText
        }
      }
    }
    https(params).then((res)=>{
      console.log(res)
      that.setData({
        getGoodsList:res.data
      })
      console.log(that.data.getGoodsList)
      if(that.data.getGoodsList.length==1){
        let t_goods=that.data.getGoodsList[0];
        if(t_goods.c_qtty==null||t_goods.c_qtty==undefined){// 无默认数量
          t_goods.c_nowNumber=1;
        }else{
          t_goods.c_nowNumber=t_goods.c_qtty;
        }
        t_goods.c_nowPrice=t_goods.c_price;
        if(t_goods.c_amount==null||t_goods.c_amount==undefined){// 散装无初始金额
          t_goods.c_nowSum=t_goods.c_nowNumber*t_goods.c_nowPrice;
        }else{
          t_goods.c_nowSum=t_goods.c_amount;
        }
        that.setData({
          singleInputFlag:true,
          editGoods:t_goods
        })
      }else if(that.data.getGoodsList.length>1){
        that.setData({
          showGoodsListFlag:true
        })
      }else{
        wx.showToast({
          title: '暂无此商品',
          icon:'error'
        })
      }
    })
  },
  // 会员搜索
  vipBtn_search(){
    var that=this;
    console.log(that.data.vipSearchText);
    if(that.data.vipSearchText==''){
      wx.showToast({
        title: '输入为空',
        icon:'error'
      })
      return false
    }
    let params={
      data:{
        "type": "cust",
        data:{
          "cardno":that.data.vipSearchText
        }
      }
    }
    that.setData({
      vipSearchText:''
    })
    https(params).then((res)=>{
      console.log(res)
      if(res.status=='成功'){
        that.setData({
          vipList:res.data
        })
      }else{
        wx.showToast({
          title: '暂无结果',
          icon:'error'
        })
      }
    })
  },
  btn_vip(){
    this.setData({
      vipAddFlag:true,
      vipList:[],
      vipSearchText:''
    })
  },
  vipBtn_choose(){
    var that=this;
    if(that.data.vipList.length>0){
      let vip=that.data.vipList[0];
      that.setData({
        vipInfo:vip,
        vipAddFlag:false
      })
    }
  },
  // 计算总价
  calcPrice(){
    var that=this;
    let sum=0;
    that.data.goodsList.forEach((t,i)=>{
      if(t.c_amount==null||t.c_amount==undefined){// 散装无初始金额
        t.c_nowSum=parseFloat(t.c_nowPrice)*parseFloat(t.c_nowNumber);
      }else{
        t.c_nowSum=t.c_amount;
      }
      sum+=t.c_nowSum;
    })
    that.setData({
      goodsList:that.data.goodsList,
      totalPrice:parseFloat(sum.toFixed(2))
    })
  },
  btn_cancelOrder(){
    var that=this;
    wx.showModal({
      title: '提示',
      content: '确定取消订单？',
      success (res) {
        if (res.confirm) {
          let params={
            data:{
              "type": "cancel",
              data:{
                "sn":that.data.waterCode
              }
            }
          }
          https(params).then((res)=>{
            console.log(res)
            that.setData({
              goodsList:[],
              vipInfo:null,
              totalPrice:0
            })
            app.clearOrder();
          })
        } else if (res.cancel) {
        }
      }
    })
  },
  btn_submit(){
    var that=this;
    if(that.data.goodsList.length==0){
      wx.showToast({
        title: '商品为空',
        icon:'error'
      })
      return false
    }
    if(that.data.vipInfo==null){
      wx.showModal({
        title: '提示',
        content: '您还没有选择会员，是否需要选择会员？',
        success (res) {
          if (res.confirm) {
            that.setData({
              vipAddFlag:true
            })
          } else if (res.cancel) {
            that.saveData();
          }
        }
      })
    }else{
      that.saveData();
    }
  },
  saveData(){
    var that=this;
    wx.setStorageSync('waitingCheckoutList', that.data.goodsList);
    let time=app.getCurrentTime();
    wx.setStorageSync('waitingCheckoutTime', time);
    wx.setStorageSync('waitingCheckoutSum', that.data.totalPrice);
    wx.setStorageSync('waitingCheckoutAlreadypaid', 0);
    wx.setStorageSync('waitingCheckoutRemainingPayment', that.data.totalPrice);
    wx.setStorageSync('vipInfo', that.data.vipInfo);
    wx.navigateTo({
      url: '../pay/index',
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    var that=this;
    try {
      var value = wx.getStorageSync('waitingCheckoutList')
      if (value) {
        wx.showModal({
          title: '提示',
          content: '请先完成上一个订单',
          showCancel:false,
          complete: (res) => {
            if (res.cancel) {
              wx.reLaunch({
                url: '../waitingSettlement/index',
              })
            }
            if (res.confirm) {
              wx.reLaunch({
                url: '../waitingSettlement/index',
              })
            }
          }
        })
      }else{
        that.getWaterCode();
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('storeInfo')
      if (value) {
        that.setData({
          storeInfo:value
        })
      }else{
      }
    } catch (e) {
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    wx.hideHomeButton();  
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})