<!--index.wxml-->
<view class="page">
  <image class="bg" src="/images/index/bg.png" mode=""/>
  <view class="header">
    <view class="line">
      <view class="search">
        <image class="iconStyle" src="/images/index/icon_search.png" mode=""/>
        <input class="input" type="number" value="{{searchText}}" bindinput="input_search" placeholder="条码/名称" placeholder-class="placeClass" />
        <image class="iconStyle4" src="/images/index/btn_close.png" bindtap="btn_clean" mode=""/>
        <button type="primary" class="btn_search" bindtap="btn_search">搜索</button>
      </view>
      <image class="iconStyle2"  bindtap="scanCode" src="/images/index/scanCode.png" mode=""/>
    </view>
    <view class="no">
      <image class="iconStyle3" src="/images/index/icon_no.png" mode=""/>
      <text class="waterCodeTitle">流水号：</text><text class="waterCode">{{waterCode}}</text>
    </view>
  </view>
  <view class="body">
    <view class="title">
      <text class="title_style1">商品/条码</text>
      <text class="title_style2">数量</text>
      <text class="title_style3">单价</text>
      <text class="title_style3">小计</text>
    </view>
    <scroll-view scroll-y="true" style="height: 700rpx;">
      <view class="goods" wx:for="{{goodsList}}">
        <van-swipe-cell right-width="{{ 65 }}">
          <van-cell-group>
            <van-cell>
              <view class="goodsName">
                <text class="goodsDetail">{{item.c_name}}</text>
                <text class="goodsDetail goodsDetail2">{{item.c_gcode}}</text>
              </view>
              <view class="goodsNum">
                <input class="goodsNumInput" type="digit" bindinput="goodsNumInput" data-index="{{index}}" value="{{item.c_nowNumber}}" disabled='{{editGoods.qtty_readonly==1}}' placeholder="请输入"/>
              </view>
              <view class="goodsPrice">
                {{item.c_nowPrice}}
              </view>
              <view class="goodsSum">
                {{item.c_nowSum}}
              </view>
            </van-cell>
          </van-cell-group>
          <view slot="right" class="btn_del" bindtap="btn_del" data-index="{{index}}">删除</view>
        </van-swipe-cell>
      </view>
      <view style="text-align: center;margin-top: 20rpx;">
        <image wx:if="{{goodsList.length==0}}" class="img_empty" src="/images/index/empty.png" mode=""/>
        <view wx:if="{{goodsList.length==0}}" class="emptyText">暂无数据</view>
        <van-button wx:if="{{goodsList.length!=0}}"  round class="btn_clearAll" bindtap="btn_clearAll" icon="delete-o" >清空商品</van-button>
      </view>
    </scroll-view>
    <view class="total">
      <view wx:if="{{goodsList.length>0}}" style="width: 100%;height: 40rpx;">
        <text class="totalCount">共{{goodsList.length}}件商品</text>
        <text class="goodsAllSum">合计：{{totalPrice}}元</text>
      </view>
      <view wx:if="{{vipInfo != null}}">
        <text class="totalCount">会员卡号：{{vipInfo.c_cardno}}</text>
        <text class="goodsAllSum">会员姓名：{{vipInfo.c_name}}</text>
      </view>
    </view>
  </view>
  <view class="watermark">
    <text class="watermarkText" wx:for="{{30}}" wx:key="index">{{storeInfo.store_name}}</text>
  </view>
  <view class="btn_group">
    <button type="primary" class="btn_cancelOrder btn_style" bindtap="btn_cancelOrder">取消订单</button>
    <button type="primary" class="btn_vip btn_style" bindtap="btn_vip">会员</button>
    <button type="primary" class="btn_pay btn_style" bindtap="btn_submit">结算</button>
  </view>
  <!-- 底部弹出层选择商品 -->
  <van-popup
  show="{{ showGoodsListFlag }}"
  closeable
  position="bottom"
  custom-style="height: 45%;padding-left:15rpx;padding-right:15rpx"
  bind:close="onClose"
>
<view class="popText">通过【{{searchText}}】找到多个商品</view>
<scroll-view scroll-y="true" style="height: 32vh;">
      <view class="goods" wx:for="{{getGoodsList}}">
        <van-swipe-cell right-width="{{ 65 }}">
          <van-cell-group>
            <van-cell >
              <view class="popGoodsName">
                <text class="popGoodsDetail">{{item.c_name}}</text>
                <text class="popGoodsDetail goodsDetail2">{{item.c_gcode}}</text>
              </view>
              <view class="popGoodsPrice">
                <text class="goodsDetail3">￥{{item.c_price}}</text>
                <text class="goodsDetail4">{{item.c_model}}</text>
              </view>
              <view class="popChoose">
                <button class="popBtn_choose" data-index="{{index}}" bindtap="popBtn_choose">选择</button>
              </view>
            </van-cell>
          </van-cell-group>
        </van-swipe-cell>
      </view>
     
    </scroll-view>
</van-popup>
<!-- 会员弹出层 -->
<van-popup show="{{ vipAddFlag }}" 
custom-style="width: 650rpx;height: 900rpx;background: #FFFFFF;border-radius: 10rpx;" bind:close="onClose" closeable>
  <view class="vipLine">
    <view class="vipSearch">
      <image class="iconStyle" src="/images/index/icon_search.png" mode=""/>
      <input class="vipInput" type="number" value="{{vipSearchText}}" bindinput="vipInput_search" placeholder="手机号/会员号" placeholder-class="placeClass" />
      <button type="primary" class="btn_search" bindtap="vipBtn_search">搜索</button>
    </view>
    <image class="vipIconStyle2"  bindtap="scanCodeVip" src="/images/index/scanCode2.png" mode=""/>
  </view>
  <view style="padding:22rpx;" >
    <view wx:for="{{vipList}}" class="vipInfoLine">
      <van-row>
        <van-col span="5" class="vipInfo">{{item.c_name}}</van-col>
        <van-col span="9" class="vipInfo">{{item.c_tele2}}</van-col>
        <van-col span="10" class="vipInfo">卡号：{{item.c_cardno}}</van-col>
      </van-row>
    </view>
  </view>
  <view class="vipChoose">
    <button class="vipBtn_choose" bindtap="vipBtn_choose">选择</button>
  </view>
</van-popup>
<!-- 商品手动输入 -->
<view class="goodsSingle" wx:if="{{singleInputFlag}}">
  <view class="formStyle1Body">
    <view class="formStyle1Line">
      <text class="formStyle1Title">商品名称</text>
      <input class="formStyle1TitleInput" type="text" value="{{editGoods.c_name}}" disabled='true' placeholder-class="formStyle1TitleInputPlaceholderStyle"/>
    </view>
    <view class="formStyle1Line">
      <text class="formStyle1Title">原价（元）</text>
      <input class="formStyle1TitleInput" type="text" value="{{editGoods.c_price}}" disabled='true' placeholder-class="formStyle1TitleInputPlaceholderStyle"/>
    </view>
    <view class="formStyle1Line">
      <text class="formStyle1Title">请输入价格</text>
      <input class="formStyle1TitleInput" style="background-color: #fff;" type="text" disabled='{{editGoods.price_readonly==1}}' value="{{editGoods.c_nowPrice}}" placeholder-class="formStyle1TitleInputPlaceholderStyle" placeholder="请填写" bindinput="input_price" />
    </view>
  </view>
  <view style="text-align: center;">
    <button type="primary" class="formStyle1Button" bindtap='saveGoods'>确定</button>
  </view>
</view>

  <van-tabbar active="{{ active }}" bind:change="onChange" z-index='3'>
    <van-tabbar-item>
      <image
        slot="icon"
        src="/images/tab/icon_home0.png"
        mode="aspectFit"
        style="width: 46rpx; height: 46rpx;"
      />
      <image
        slot="icon-active"
        src="/images/tab/icon_home1.png"
        mode="aspectFit"
        style="width: 46rpx; height: 46rpx;"
      />
      首页
    </van-tabbar-item>
    <van-tabbar-item>
      <image
        slot="icon"
        src="/images/tab/icon_padding0.png"
        mode="aspectFit"
        style="width: 46rpx; height: 46rpx;"
      />
      <image
        slot="icon-active"
        src="/images/tab/icon_padding1.png"
        mode="aspectFit"
        style="width: 46rpx; height: 46rpx;"
      />
    待结算
    </van-tabbar-item>
    <van-tabbar-item>
      <image
        slot="icon"
        src="/images/tab/icon_personal0.png"
        mode="aspectFit"
        style="width: 46rpx; height: 46rpx;"
      />
      <image
        slot="icon-active"
        src="/images/tab/icon_personal1.png"
        mode="aspectFit"
        style="width: 46rpx; height: 46rpx;"
      />
    我的
    </van-tabbar-item>
  </van-tabbar>
</view>
