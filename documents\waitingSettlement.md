# 待结算页 (waitingSettlement) 文档

## 1. 页面概述

待结算页是收银员进行商品录入、管理待结算商品列表、添加会员以及最终发起结算的核心页面。它支持商品搜索（条码/名称）、扫码录入、手动修改商品数量和价格、删除商品、清空商品列表、添加会员（搜索/扫码）等功能。页面顶部显示流水号，底部有取消订单、会员和结算按钮，并包含一个底部导航栏。

## 2. 文件结构

- <mcfile name="index.js" path="e:\miniprogram\yunpos\pages\waitingSettlement\index.js"></mcfile>: 页面逻辑文件，处理商品和会员的搜索、增删改查、数据计算、页面跳转和生命周期管理。
- <mcfile name="index.json" path="e:\miniprogram\yunpos\pages\waitingSettlement\index.json"></mcfile>: 页面配置文件，定义导航栏样式和标题，以及引入Vant Weapp组件。
- <mcfile name="index.wxml" path="e:\miniprogram\yunpos\pages\waitingSettlement\index.wxml"></mcfile>: 页面结构文件，负责UI布局和数据绑定，包含商品列表、会员信息、底部操作区和弹出层。
- <mcfile name="index.wxss" path="e:\miniprogram\yunpos\pages\waitingSettlement\index.wxss"></mcfile>: 页面样式文件，定义页面元素的样式。

## 3. 页面配置 (<mcfile name="index.json" path="e:\miniprogram\yunpos\pages\waitingSettlement\index.json"></mcfile>)

```json
{
  "navigationBarTextStyle": "white",
  "navigationBarTitleText": "待结算",
  "backgroundColor": "#FAF8F5",
  "backgroundTextStyle": "light",
  "usingComponents": {}
}
```

- `navigationBarTextStyle`: 导航栏标题文字颜色为白色。
- `navigationBarTitleText`: 导航栏标题显示为“待结算”。
- `backgroundColor`: 页面背景颜色设置为浅灰色。
- `usingComponents`: 引入了Vant Weapp的 `van-popup`, `van-swipe-cell`, `van-cell-group`, `van-cell`, `van-button`, `van-row`, `van-col`, `van-tabbar`, `van-tabbar-item` 等组件。

## 4. 页面逻辑 (<mcfile name="index.js" path="e:\miniprogram\yunpos\pages\waitingSettlement\index.js"></mcfile>)

### 4.1. 页面数据 (`data`)

```javascript
data: {
  searchText: '', // 商品搜索框内容
  vipSearchText: '', // 会员搜索框内容
  waterCode: '', // 流水号
  active: 1, // 底部导航栏当前激活项的索引，1代表“待结算”
  showGoodsListFlag: false, // 控制商品选择弹出层显示/隐藏
  singleInputFlag: false, // 控制商品手动输入弹出层显示/隐藏
  vipAddFlag: false, // 控制会员添加弹出层显示/隐藏
  totalPrice: 0, // 商品总价
  editGoods: {}, // 正在编辑的商品信息
  getGoodsList: [], // 搜索到的商品列表
  goodsList: [], // 待结算商品列表
  vipList: [], // 会员搜索结果列表
  vipInfo: null, // 已选择的会员信息
  storeInfo: {}, // 商店信息
},
```

### 4.2. 页面方法

- `scanCode()`: 扫码录入商品。
- `scanCodeVip()`: 扫码添加会员。
- `btn_clean()`: 清空商品搜索框内容。
- `onChange(e)`: 底部导航栏切换事件，根据索引跳转页面，若有未结算商品则提示。
- `onClose()`: 关闭商品选择和会员添加弹出层。
- `getWaterCode()`: 获取流水号，并检查流水号日期是否过期，若过期则取消订单并返回首页。
- `getGoods()`: 从本地缓存获取待结算商品列表。
- `getVip()`: 从本地缓存获取会员信息。
- `btn_del(e)`: 删除待结算商品列表中的指定商品。
- `goodsNumInput(e)`: 修改商品数量，并重新计算小计和总价。
- `input_price(e)`: 手动输入商品价格，并重新计算小计和总价。
- `btn_search()`: 商品搜索，根据 `searchText` 调用 `https` 服务搜索商品，并处理搜索结果（单个商品直接添加，多个商品弹出选择列表）。
- `popBtn_choose(e)`: 从商品选择弹出层中选择商品并添加到待结算列表。
- `btn_clearAll()`: 清空所有待结算商品。
- `btn_cancelOrder()`: 取消当前订单，清空所有相关缓存数据并返回首页。
- `btn_vip()`: 打开会员添加弹出层。
- `vipInput_search(e)`: 会员搜索框输入事件。
- `vipBtn_search()`: 会员搜索，根据 `vipSearchText` 调用 `https` 服务搜索会员。
- `vipBtn_choose()`: 从会员搜索结果中选择会员并添加到订单。
- `saveGoods()`: 保存手动输入的商品价格。
- `btn_submit()`: 结算订单，将订单数据保存到本地缓存并跳转到结算页面。
- `calculateTotalPrice()`: 计算待结算商品的总价。

### 4.3. 生命周期

- `onLoad(options)`: 页面加载时触发，初始化流水号、商品列表、会员信息和商店信息。
- `onShow()`: 页面显示时触发，重新初始化页面数据，确保数据最新。

### 4.4. 依赖

- 引入了 `../../utils/service` 模块，用于进行网络请求。

## 5. 页面结构 (<mcfile name="index.wxml" path="e:\miniprogram\yunpos\pages\waitingSettlement\index.wxml"></mcfile>)

页面主要包含以下部分：

- **头部区域 (`header`)**:
    - **商品搜索**: 包含搜索框、搜索按钮和扫码按钮。
    - **流水号显示**: 显示当前订单的流水号 (`waterCode`)。
- **主体内容区域 (`body`)**:
    - **商品列表标题**: 显示“商品/条码”、“数量”、“单价”、“小计”等列标题。
    - **待结算商品列表**: 使用 `scroll-view` 和 `van-swipe-cell` 展示商品列表，支持左滑删除。
    - **空状态提示**: 当 `goodsList` 为空时显示“暂无数据”图片和文字。
    - **清空商品按钮**: 当 `goodsList` 不为空时显示。
    - **总计信息**: 显示商品总件数和总价，以及已添加的会员信息。
- **水印**: 页面背景上的水印，显示 `storeInfo.store_name`。
- **底部操作按钮区 (`btn_group`)**:
    - “取消订单”、“会员”、“结算”三个按钮。
- **商品选择弹出层 (`van-popup`)**:
    - 当搜索到多个商品时显示，供用户选择。
- **会员添加弹出层 (`van-popup`)**:
    - 供用户搜索和选择会员。
- **商品手动输入弹出层 (`goodsSingle`)**:
    - 供用户手动输入商品价格。
- **底部导航栏 (`van-tabbar`)**:
    - 包含“首页”、“待结算”和“我的”三个Tab。

## 6. 样式 (<mcfile name="index.wxss" path="e:\miniprogram\yunpos\pages\waitingSettlement\index.wxss"></mcfile>)

样式文件定义了页面的布局和视觉效果，包括：

- **全局样式**: `page`, `bg` 定义了页面背景和整体布局。
- **头部样式**: `header`, `line`, `search`, `input`, `iconStyle`, `btn_search`, `iconStyle2`, `placeClass`, `no`, `iconStyle3`, `iconStyle4`, `waterCodeTitle`, `waterCode` 定义了搜索区和流水号区的布局和样式。
- **主体样式**: `body`, `title`, `title_style1`, `title_style2`, `title_style3`, `goods`, `goodsName`, `goodsDetail`, `goodsDetail2`, `goodsNum`, `goodsNumInput`, `goodsPrice`, `goodsSum`, `btn_del` 定义了商品列表的布局和样式。
- **空状态和总计样式**: `img_empty`, `emptyText`, `btn_clearAll`, `total`, `totalCount`, `goodsAllSum`。
- **水印样式**: `watermark`, `watermarkText`。
- **底部按钮样式**: `btn_group`, `btn_cancelOrder`, `btn_vip`, `btn_pay`, `btn_style`。
- **弹出层样式**: `popText`, `popGoodsName`, `popGoodsDetail`, `popGoodsPrice`, `goodsDetail3`, `goodsDetail4`, `popChoose`, `popBtn_choose`, `vipLine`, `vipSearch`, `vipInput`, `vipIconStyle2`, `vipInfoLine`, `vipInfo`, `vipChoose`, `vipBtn_choose`, `goodsSingle`, `formStyle1Body`, `formStyle1Line`, `formStyle1Title`, `formStyle1TitleInput`, `formStyle1TitleInputPlaceholderStyle`, `formStyle1Button`。

## 7. 数据流程与实现方法

1.  **页面加载与初始化**: `onLoad` 和 `onShow` 生命周期中，页面会从本地缓存加载 `waterCode`、`goodsList`、`vipInfo` 和 `storeInfo`，并调用 `getWaterCode` 检查流水号有效性。
2.  **商品录入**: 
    - **搜索**: 用户输入条码/名称，点击搜索或扫码，调用 `btn_search`。如果搜索结果唯一，直接添加到 `goodsList`；如果多个，弹出 `showGoodsListFlag` 供选择；如果未找到，提示用户。
    - **手动修改**: 用户可以直接在列表中修改商品数量，或通过手动输入弹出层修改价格。
3.  **商品管理**: 
    - **删除**: 左滑商品项，点击删除按钮，调用 `btn_del` 从 `goodsList` 中移除商品。
    - **清空**: 点击“清空商品”按钮，调用 `btn_clearAll` 清空 `goodsList`。
4.  **会员添加**: 
    - 点击“会员”按钮或扫码，打开 `vipAddFlag` 弹出层。
    - 用户输入手机号/会员号搜索，或扫码获取会员信息。
    - 选择会员后，`vipInfo` 更新，并显示在页面总计区域。
5.  **数据计算**: 每次 `goodsList` 变化时，调用 `calculateTotalPrice` 重新计算商品总价。
6.  **订单取消**: 点击“取消订单”按钮，调用 `btn_cancelOrder` 清空所有相关缓存并返回首页。
7.  **结算**: 点击“结算”按钮，调用 `btn_submit` 将当前订单数据保存到本地缓存，并跳转到 `pay` 页面进行支付。

## 8. 注意事项

- **本地缓存**: 页面大量使用 `wx.setStorageSync` 和 `wx.getStorageSync` 来存储和获取订单数据，确保数据的持久性和一致性。
- **流水号管理**: `getWaterCode` 方法实现了流水号的日期校验和过期订单的自动取消，这对于订单的生命周期管理非常重要。
- **Vant Weapp**: 页面广泛使用了Vant Weapp组件，确保其正确引入和使用。
- **网络请求**: 依赖于 `https` 服务进行商品和会员的搜索，确保后端接口的可用性。
- **用户体验**: 提供了扫码、搜索、手动输入等多种商品录入方式，以及左滑删除、清空等便捷操作，提升用户体验。