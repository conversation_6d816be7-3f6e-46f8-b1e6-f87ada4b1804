page{
  background: #FAF8F5;
}
.page{
  width: 100%;
  height: 100%;
}
.bg{
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 220rpx;
  z-index: -1;
}
.header{
  position: relative;
  padding-left: 22rpx;
}
.userLine{
  margin-top: 20rpx;
}
.iconStyle{
  width: 92rpx;
  height: 92rpx;
}
.userInfo{
  display: inline-block;
  margin-left: 18rpx;
  height: 92rpx;
  vertical-align: top;
}
.name{
  width: 200rpx;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  line-height: 60rpx;
  height: 60rpx;
  font-size: 34rpx;
  color: #FFFFFF;
}
.tel{
  width: 200rpx;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  line-height: 20rpx;
  height: 20rpx;
  font-size: 24rpx;
  color: #C0DAFF;
}
.address{
  float: right;
  margin-top: 46rpx;
}
.addressDetail{
  line-height: 20rpx;
  height: 20rpx;
  font-size: 24rpx;
  color: #C0DAFF;
  margin-right: 30rpx;
}
.banner1{
  width: 714rpx;
  /* height: 80rpx; */
  padding-top: 40rpx;
  padding-bottom: 24rpx;
  background: #FFFFFF;
  border-radius: 14rpx;
  position: absolute;
  bottom: -160rpx;
}
.banner1Title1 view{
  text-align: center;
  font-size: 36rpx;
  color: #181818;
  margin-bottom: 30rpx;
  height: 28rpx;
  line-height: 28rpx;
}
.banner1Title2 view{
  text-align: center;
  font-size: 24rpx;
  color: #808080;
  height: 24rpx;
  line-height: 24rpx;
}
.banner1Line{
  width: 1rpx;
  height: 71rpx;
  background: #DCDCDC;
  position: absolute;
  left: 50%;
  bottom: 30rpx;
}
.banner2{
  width: 714rpx;
  height: 150rpx;
  margin-left: 18rpx;
  margin-top: 180rpx;
}
.iconStyle2{
  width: 100%;
  height: 100%;
}
.body{
  width: 714rpx;
  background-color: #FFFFFF;
  border-radius: 14rpx;
  margin-top: 178rpx;
  margin-left: 18rpx;
}
.bodyLine{
  padding-left: 30rpx;
  padding-right: 30rpx;
  height: 100rpx;
  line-height: 100rpx;
  border-bottom: 1px solid #F0F0F0;
}
.title2{
  font-size: 28rpx;
  color: #262626;
}
.iconStyle3{
  width: 40rpx;
  height: 40rpx;
  vertical-align: middle;
  margin-right: 20rpx;
}
.iconStyle4{
  width: 30rpx;
  height: 30rpx;
}
.title5 view{
  height: 100rpx;
  line-height: 100rpx;
  text-align: right;
}