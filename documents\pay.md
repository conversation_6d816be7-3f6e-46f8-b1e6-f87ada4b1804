# 结算页 (pay) 文档

## 1. 页面概述

结算页用于展示待支付订单的详细信息，包括商品列表、流水号、时间、会员信息（如果已添加）、应付金额、已付金额、剩余支付金额以及付款明细。用户可以在此页面选择不同的支付方式进行支付。

## 2. 文件结构

- <mcfile name="index.js" path="e:\miniprogram\yunpos\pages\pay\index.js"></mcfile>: 页面逻辑文件，处理数据初始化、支付方式获取、支付跳转和返回订单等功能。
- <mcfile name="index.json" path="e:\miniprogram\yunpos\pages\pay\index.json"></mcfile>: 页面配置文件，定义导航栏样式和标题。
- <mcfile name="index.wxml" path="e:\miniprogram\yunpos\pages\pay\index.wxml"></mcfile>: 页面结构文件，负责UI布局和数据绑定。
- <mcfile name="index.wxss" path="e:\miniprogram\yunpos\pages\pay\index.wxss"></mcfile>: 页面样式文件，定义页面元素的样式。

## 3. 页面配置 (<mcfile name="index.json" path="e:\miniprogram\yunpos\pages\pay\index.json"></mcfile>)

```json
{
  "navigationBarTextStyle": "white",
  "navigationBarTitleText": "结算",
  "backgroundColor": "#0C6FF9",
  "backgroundTextStyle": "light",
  "usingComponents": {}
}
```

- `navigationBarTextStyle`: 导航栏标题文字颜色为白色。
- `navigationBarTitleText`: 导航栏标题显示为“结算”。
- `backgroundColor`: 页面背景颜色设置为深蓝色。

## 4. 页面逻辑 (<mcfile name="index.js" path="e:\miniprogram\yunpos\pages\pay\index.js"></mcfile>)

### 4.1. 页面数据 (`data`)

```javascript
data: {
  goodsList: [], // 商品列表
  vipInfo: null, // 会员信息
  waterCode: '', // 流水号
  time: '', // 订单时间
  pay_channels: [], // 支付方式列表
  payList: [], // 付款明细列表
  waitingCheckoutSum: 0, // 应付金额
  waitingCheckoutAlreadypaid: 0, // 已支付金额
  waitingCheckoutRemainingPayment: 0, // 剩余支付金额
},
```

### 4.2. 页面方法

- `payWay_search()`: 获取支付方式列表。
    - 调用 `https` 服务发送获取支付渠道请求 (`type: "pay_channels"`)。
    - 更新 `pay_channels` 数据，并将其存储到本地缓存。
- `btn_link(e)`: 支付方式列表项点击事件，根据 `data-id` 跳转到不同的支付子页面。
    - `id='1000'`: 跳转到储值卡支付 (`valueCard`)
    - `id='2000'`: 跳转到满减优惠 (`discount`)，限制只能使用一次满减。
    - `id='3000'`: 跳转到优惠券支付 (`coupons?id=3000`)
    - `id='4000'`: 跳转到微信支付 (`wechat?id=4000`)
    - `id='5000'`: 跳转到银行卡支付列表 (`bankCardList?id=5000`)
- `goBack()`: 返回订单页面。
    - 如果 `goodsList` 不为空，跳转到待结算页面 (`../waitingSettlement/index`)。
    - 否则，跳转到首页 (`../index/index`)。
- `initPage()`: 初始化页面数据。
    - 从本地缓存 (`wx.getStorageSync`) 中读取 `waitingCheckoutList`, `waterCode`, `waitingCheckoutTime`, `vipInfo`, `waitingCheckoutSum`, `waitingCheckoutAlreadypaid`, `waitingCheckoutRemainingPayment`, `payList` 等数据，并更新到页面的 `data` 中。

### 4.3. 生命周期

- `onLoad(options)`: 页面加载时触发。
    - 调用 `that.payWay_search()` 获取支付方式。
    - 调用 `that.initPage()` 初始化页面数据。

### 4.4. 依赖

- 引入了 `../../utils/service` 模块，用于进行网络请求。

## 5. 页面结构 (<mcfile name="index.wxml" path="e:\miniprogram\yunpos\pages\pay\index.wxml"></mcfile>)

页面主要包含一个可滚动的视图 (`scroll-view`)，内部结构分为几个主要部分：

- **订单信息区域**: 显示流水号 (`waterCode`)、订单时间 (`time`) 和会员信息 (`vipInfo.c_name`)。
- **商品列表区域**: 遍历 `goodsList` 数组，显示每个商品的名称 (`item.c_name`)、编码 (`item.c_gcode`)、数量 (`item.c_nowNumber`)、成交价 (`item.c_nowPrice`) 和小计 (`item.c_nowSum`)。
- **价格明细区域**: 显示应付金额 (`waitingCheckoutSum`)、已付金额 (`waitingCheckoutAlreadypaid`) 和剩余支付金额 (`waitingCheckoutRemainingPayment`)。
- **付款明细区域**: 遍历 `payList` 数组，显示每条付款记录的支付类型 (`item.c_type`)、支付金额 (`item.writeoff_amount`) 和支付时间 (`item.writeoff_time`)。
- **支付方式区域**: 遍历 `pay_channels` 数组，显示可用的支付方式名称 (`item.name`)，点击可跳转到相应的支付子页面。
- **返回按钮**: “返回订单”按钮，点击调用 `goBack` 方法。

## 6. 样式 (<mcfile name="index.wxss" path="e:\miniprogram\yunpos\pages\pay\index.wxss"></mcfile>)

样式文件定义了页面的布局和视觉效果，包括：

- **全局样式**: `page` 定义了页面背景和内边距。
- **模块样式**: `.section` 定义了各个信息模块的背景、内边距和圆角。
- **文本样式**: `title1`, `title2`, `title3`, `title4`, `title6` 定义了不同文本元素的字体大小和颜色。
- **商品列表样式**: `goods`, `goodsName`, `goodsDetail`, `goodsDetail2`, `goodsNum`, `goodsPrice`, `goodsSum` 定义了商品列表项的布局和文本样式。
- **图标样式**: `iconStyle`, `iconStyle2` 定义了页面中使用的图标大小和对齐方式。
- **按钮样式**: `btn_addVip`, `goBack` 定义了按钮的样式。

## 7. 数据流程与实现方法

1.  **页面加载**: `onLoad` 时，首先调用 `payWay_search` 获取支付方式列表，然后调用 `initPage` 从本地缓存加载订单相关数据。
2.  **数据初始化**: `initPage` 从本地缓存读取待结算商品、流水号、时间、会员信息、金额明细和已有的付款明细，并更新页面数据。
3.  **支付方式获取**: `payWay_search` 通过网络请求获取可用的支付渠道列表，更新页面数据并保存到本地缓存。
4.  **UI展示**: 页面根据 `data` 中的数据渲染商品列表、金额明细、付款明细和支付方式列表。
5.  **支付跳转**: 用户点击支付方式列表项，`btn_link` 方法根据点击项的 `id` 跳转到相应的支付子页面，如储值卡、满减、优惠券、微信支付、银行卡支付等。
6.  **返回订单**: 用户点击“返回订单”按钮，`goBack` 方法根据当前是否有商品列表决定是返回待结算页面还是首页。

## 8. 注意事项

- **本地缓存**: 页面大量依赖本地缓存来传递和存储订单相关数据，需要注意缓存的有效期和数据一致性。
- **支付子页面**: 具体的支付逻辑（如调用微信支付API、处理银行卡支付流程等）在跳转的子页面中实现。
- **满减限制**: `btn_link` 方法中对满减 (`id='2000'`) 做了只能使用一次的限制。
- **会员信息**: 会员信息 (`vipInfo`) 的显示是可选的，取决于待结算订单是否关联了会员。
- **网络请求**: 依赖于 `https` 服务进行支付方式的获取，确保网络请求的稳定性和安全性。