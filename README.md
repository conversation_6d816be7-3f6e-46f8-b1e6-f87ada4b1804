# 云POS小程序项目文档

## 1. 项目概述

本项目是一个基于微信小程序平台开发的云POS系统。旨在提供便捷的收银、订单管理、会员管理、优惠券核销等功能，适用于各类零售门店。

## 2. 目录结构

```
.eslintrc.js             // ESLint配置文件
.gitignore               // Git忽略文件配置
README.en.md             // 英文版项目说明（如果存在）
README.md                // 项目主文档
app.js                   // 小程序入口文件，负责全局逻辑、生命周期和API封装
app.json                 // 小程序全局配置，包括页面路径、窗口表现、网络超时等
app.wxss                 // 小程序全局样式文件
images/                  // 存放项目图片资源
├── index/               // 首页相关图片
├── login/               // 登录页相关图片
├── mine/                // 我的页面相关图片
├── pay/                 // 支付页面相关图片
└── tab/                 // 底部导航栏相关图片
miniprogram_npm/         // 小程序npm包目录，存放第三方库
├── @vant/               // Vant Weapp UI组件库
└── jsbn/                // JavaScript大数运算库
└── miniprogram-sm-crypto/ // 国密算法库
package-lock.json        // npm包锁定文件
package.json             // npm包配置文件，记录项目依赖
pages/                   // 存放所有页面
├── index/               // 首页
├── login/               // 登录页
├── logs/                // 日志页（可能用于调试或示例）
├── mine/                // 我的页面
│   ├── feedback.js      // 意见反馈页面
│   ├── index.js         // 我的页面主文件
│   ├── record.js        // 记录页面
│   └── setting.js       // 设置页面
├── pay/                 // 支付相关页面
│   ├── bankCard.js      // 银行卡页面
│   ├── bankCardList.js  // 银行卡列表页面
│   ├── coupons.js       // 优惠券页面
│   ├── couponsDetail.js // 优惠券详情页面
│   ├── couponsVipDetail.js // 会员优惠券详情页面
│   ├── couponsVipList.js // 会员优惠券列表页面
│   ├── discount.js      // 折扣页面
│   ├── index.js         // 支付主页面
│   ├── result.js        // 支付结果页面
│   ├── valueCard.js     // 储值卡页面
│   └── wechat.js        // 微信支付页面
└── waitingSettlement/   // 待结算页面
project.config.json      // 小程序项目配置文件，包含项目设置、编译模式等
project.private.config.json // 小程序私有项目配置文件
sitemap.json             // 小程序页面索引配置，用于SEO
utils/                   // 工具类文件
├── service.js           // 服务请求封装，可能包含网络请求、API接口定义等
└── util.js              // 通用工具函数
```

## 3. 主要文件说明

- <mcfile name="app.js" path="e:\miniprogram\yunpos\app.js"></mcfile>: 小程序的入口文件，负责全局的生命周期管理、全局数据共享以及一些公共方法的封装。例如，其中包含了HTTP请求的封装 (`https` 方法)。
- <mcfile name="app.json" path="e:\miniprogram\yunpos\app.json"></mcfile>: 小程序的全局配置文件，定义了所有页面路径、底部tabBar、窗口样式、网络超时等。
- <mcfile name="app.wxss" path="e:\miniprogram\yunpos\app.wxss"></mcfile>: 小程序的全局样式文件，定义了可以在所有页面中使用的公共样式。
- `pages/`: 目录下每个子文件夹代表一个小程序页面，每个页面通常包含 `.js` (逻辑)、`.json` (配置)、`.wxml` (结构) 和 `.wxss` (样式) 四个文件。
- <mcfile name="utils/service.js" path="e:\miniprogram\yunpos\utils\service.js"></mcfile>: 封装了与后端进行数据交互的服务请求，例如API接口的定义和调用。
- <mcfile name="utils/util.js" path="e:\miniprogram\yunpos\utils\util.js"></mcfile>: 存放项目中的通用工具函数，如日期格式化、数据校验等。
- `miniprogram_npm/`: 存放通过 `npm` 安装的第三方依赖库，例如 `Vant Weapp` 用于提供丰富的UI组件。

## 4. 开发环境配置

1. **安装微信开发者工具**: 从微信官方网站下载并安装最新版微信开发者工具。
2. **导入项目**: 打开微信开发者工具，选择“导入项目”，选择本项目根目录 `e:\miniprogram\yunpos`，并填写正确的AppID。
3. **安装依赖**: 在项目根目录下，打开命令行工具（如PowerShell），运行 `npm install` 安装项目依赖。
4. **构建npm**: 在微信开发者工具中，点击“工具” -> “构建npm”，确保第三方库能够正确引入。

## 5. 运行与调试

1. **预览**: 在微信开发者工具中，点击“预览”按钮，可以使用微信扫码在手机上预览小程序效果。
2. **真机调试**: 点击“真机调试”按钮，可以在手机上进行真机调试，查看日志、网络请求等。
3. **模拟器调试**: 开发者工具内置模拟器，可以直接在模拟器中运行和调试小程序。

## 6. 注意事项

- **AppID配置**: 确保 `project.config.json` 中的 `appid` 配置正确，否则可能无法正常预览或发布。
- **网络请求**: 小程序的所有网络请求域名需要在微信公众平台进行配置，否则会报错。
- **UI组件库**: 本项目使用了 `Vant Weapp` 组件库，请查阅其官方文档了解更多组件使用方法。
- **API接口**: 后端API接口的联调请参考 <mcfile name="utils/service.js" path="e:\miniprogram\yunpos\utils\service.js"></mcfile> 文件中的定义和调用方式。
