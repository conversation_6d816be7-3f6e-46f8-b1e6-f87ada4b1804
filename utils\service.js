

// 400-500之间有个过期，重新请求token接口422


export const https = (params)=>{
  var app=getApp();
  const baseUrl='https://yunpos.cztfjt.cn/ofpost';
  let header={
    'content-type': 'application/json;charset=UTF-8'
  }
  let access_token=app.globalData.access_token;
  console.log("access_token=",access_token)
  let refresh_token=app.globalData.refresh_token;
  if(access_token!=''){
    header.Authorization='Bearer '+access_token;
  }
  wx.showLoading({
    title: '请求中',
  })
  return new Promise((resolve,reject)=>{
    wx.request({
      url:baseUrl,
      data: params.data||{},
      method:params.method||'POST',
      header: header,
      success:(res)=> {
        wx.hideLoading()
        if(res.data.status=='失败'){
          app.getAccessToken();
        }
        console.log(res.data)
        resolve(res.data); 
      },
      fail:(error)=>{
        wx.hideLoading()
        console.log(error)
        reject(error)
      }
    })
  })
}