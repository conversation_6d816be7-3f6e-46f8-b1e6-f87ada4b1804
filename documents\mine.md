# 我的页面 (mine) 文档

## 1. 页面概述

“我的”页面是用户个人中心，展示收银员信息、当日/本周营业额，并提供今日记录、时段查询、反馈和设置等功能入口。页面底部包含一个自定义的底部导航栏，用于在首页、待结算和我的页面之间切换。

## 2. 文件结构

- <mcfile name="index.js" path="e:\miniprogram\yunpos\pages\mine\index.js"></mcfile>: 页面逻辑文件，处理数据获取、事件响应和页面跳转。
- <mcfile name="index.json" path="e:\miniprogram\yunpos\pages\mine\index.json"></mcfile>: 页面配置文件，定义导航栏样式和标题。
- <mcfile name="index.wxml" path="e:\miniprogram\yunpos\pages\mine\index.wxml"></mcfile>: 页面结构文件，负责UI布局和数据绑定。
- <mcfile name="index.wxss" path="e:\miniprogram\yunpos\pages\mine\index.wxss"></mcfile>: 页面样式文件，定义页面元素的样式。

## 3. 页面配置 (<mcfile name="index.json" path="e:\miniprogram\yunpos\pages\mine\index.json"></mcfile>)

```json
{
  "navigationBarTextStyle": "white",
  "navigationBarTitleText": "我的",
  "backgroundColor": "#FAF8F5",
  "backgroundTextStyle": "light",
  "usingComponents": {}
}
```

- `navigationBarTextStyle`: 导航栏标题文字颜色为白色。
- `navigationBarTitleText`: 导航栏标题显示为“我的”。
- `backgroundColor`: 页面背景颜色设置为浅灰色。

## 4. 页面逻辑 (<mcfile name="index.js" path="e:\miniprogram\yunpos\pages\mine\index.js"></mcfile>)

### 4.1. 页面数据 (`data`)

```javascript
data: {
  active: 2, // 底部导航栏当前激活项的索引，2代表“我的”
  vipInfo: null, // 会员信息，目前未用到
  day_amount: 0, // 本日营业额
  week_amount: 0, // 本周营业额
  employeeInfo: {}, // 收银员信息
},
```

### 4.2. 页面方法

- `onChange(e)`: 底部导航栏切换事件。
    - 根据 `e.detail` (激活项索引) 跳转到对应的页面：
        - `index=0`: 跳转到首页 (`../index/index`)
        - `index=1`: 跳转到待结算页面 (`../waitingSettlement/index`)
- `btn_link(e)`: 列表项点击事件，根据 `data-index` 跳转到不同子页面。
    - `index=0`: 跳转到今日记录 (`record?id=0`)
    - `index=1`: 跳转到时段查询 (`record?id=1`)
    - `index=4`: 跳转到反馈页面 (`feedback`)
    - `index=5`: 跳转到设置页面 (`setting`)
- `vipBtn_search()`: 会员搜索功能（代码存在但未在WXML中找到对应UI，可能为预留功能或已废弃）。
    - 检查搜索输入是否为空。
    - 调用 `https` 服务发送会员查询请求。
    - 更新 `vipList` 数据。
- `getSales()`: 获取营业额数据。
    - 调用 `https` 服务发送获取营业额请求 (`type: "amount"`)。
    - 更新 `day_amount` 和 `week_amount` 数据。
- `getEmployeeInfo()`: 获取收银员信息。
    - 调用 `https` 服务发送获取收银员信息请求 (`type: "employee"`)。
    - 更新 `employeeInfo` 数据。

### 4.3. 生命周期

- `onLoad(options)`: 页面加载时触发。
    - 调用 `this.getSales()` 获取营业额数据。
    - 调用 `this.getEmployeeInfo()` 获取收银员信息。

## 5. 页面结构 (<mcfile name="index.wxml" path="e:\miniprogram\yunpos\pages\mine\index.wxml"></mcfile>)

页面主要包含以下部分：

- **头部区域 (`header`)**:
    - **用户信息**: 显示收银员头像、姓名 (`employeeInfo.username`)、电话 (`employeeInfo.tel`)、部门名称 (`employeeInfo.department_name`) 和部门代码 (`employeeInfo.dept_code`)。
    - **营业额展示**: 使用 `van-row` 和 `van-col` 布局，显示本日营业额 (`day_amount`) 和本周营业额 (`week_amount`)。
- **主体内容区域 (`body`)**:
    - 多个 `bodyLine` 列表项，每个列表项包含图标、文字和右侧箭头，点击可跳转到不同功能页面（今日记录、时段查询、反馈、设置）。
- **底部导航栏 (`van-tabbar`)**:
    - 使用 Vant Weapp 的 `van-tabbar` 组件，包含“首页”、“待结算”和“我的”三个Tab。
    - `active` 属性绑定 `data.active` 控制当前选中项。
    - `bind:change` 绑定 `onChange` 方法处理Tab切换逻辑。

## 6. 样式 (<mcfile name="index.wxss" path="e:\miniprogram\yunpos\pages\mine\index.wxss"></mcfile>)

样式文件定义了页面的布局和视觉效果，包括：

- **全局样式**: `page` 和 `bg` 定义了页面背景和整体布局。
- **头部样式**: `header`, `userLine`, `iconStyle`, `userInfo`, `name`, `tel`, `address`, `addressDetail` 定义了用户信息区域的布局和文字样式。
- **营业额区域样式**: `banner1`, `banner1Title1`, `banner1Title2`, `banner1Line` 定义了营业额展示区域的布局和文字样式。
- **主体列表样式**: `body`, `bodyLine`, `title2`, `iconStyle3`, `iconStyle4`, `title5` 定义了功能列表项的布局和图标样式。

## 7. 数据流程与实现方法

1.  **页面加载**: `onLoad` 时，页面会立即调用 `getSales()` 和 `getEmployeeInfo()` 方法。
2.  **数据请求**: `getSales()` 和 `getEmployeeInfo()` 方法通过 `https` 服务（推测是封装了 `wx.request` 的网络请求工具）向后端发送请求，获取营业额和收银员信息。
3.  **数据更新**: 请求成功后，返回的数据会通过 `that.setData()` 更新到页面的 `data` 中，从而驱动WXML页面进行渲染。
4.  **UI交互**: 
    - 用户点击底部导航栏，`onChange` 方法根据点击的Tab索引进行页面跳转。
    - 用户点击功能列表项，`btn_link` 方法根据 `data-index` 进行页面跳转，传递参数以便子页面识别功能类型。

## 8. 注意事项

- **Vant Weapp**: 页面使用了 Vant Weapp 组件库 (`van-row`, `van-col`, `van-tabbar`)，确保已正确引入和配置。
- **网络请求**: 依赖于 `https` 服务进行数据交互，需要确保后端接口的可用性和正确性。
- **图片资源**: 页面中使用了 `/images/mine/` 和 `/images/tab/` 路径下的图片资源，确保这些图片文件存在。
- **路由管理**: 页面跳转使用了 `wx.reLaunch` 和 `wx.navigateTo`，需要注意它们之间的区别和适用场景。
- **未使用的功能**: `vipInfo` 和 `vipBtn_search` 相关代码存在，但WXML中未找到对应的UI元素，可能为未完成或已废弃的功能。