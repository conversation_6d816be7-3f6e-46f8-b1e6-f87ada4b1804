/* pages/pay/index.wxss */
page{
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #FAF8F5;
}
.page{
  width: calc(100% - 36rpx);
  height: 100%;
  padding-left: 18rpx;
  padding-right: 18rpx;
}
.section{
  background-color: #fff;
  padding: 12rpx 28rpx;
  border-radius: 14rpx;
  margin-bottom: 18rpx;
}
.noVip{
  text-align: center;
  font-size: 24rpx;
  color: #3A3A3A;
  height: 70rpx;
  line-height: 70rpx;
}
.title1{
  font-size: 24rpx;
  color: #9E9E9E;
  height: 60rpx;
  line-height: 60rpx;
}
.btn_addVip{
  width: 150rpx;
  height: 58rpx;
  background: #0C72FF;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #FFFFFF;
}
.iconStyle2{
  width: 30rpx;
  height: 30rpx;
  vertical-align: middle;
}
.goods{
  height: 114rpx;
}
.goodsName{
  display: inline-block;
  width: 244rpx;
  padding-left: 10rpx;
  line-height: 57rpx;
}
.goodsDetail{
  display: inline-block;
  width: 244rpx;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  line-height: 28rpx;
  height: 28rpx;
  font-size: 26rpx;
  color: #181818;
}
.goodsDetail2{
  font-size: 24rpx;
  color: #444444;
}
.goodsNum{
  width: 80rpx;
  display: inline-block;
  line-height: 57rpx;
  height: 57rpx;
  vertical-align: top;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  font-size: 26rpx;
  color: #181818;
}
.goodsPrice,.goodsSum{
  width: 160rpx;
  display: inline-block;
  line-height: 57rpx;
  height: 57rpx;
  vertical-align: top;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  font-size: 26rpx;
  color: #181818;
}
/* 模块2 价目表 */
.title2 view{
  font-size: 24rpx;
  color: #181818;
  margin-bottom: 24rpx;
}
.title3{
  font-size: 24rpx;
  color: #FF1A1A
}
/* 付款明细 */
.title4{
  font-size: 28rpx;
  color: #181818;
  height: 40rpx;
  line-height: 40rpx;
  margin-bottom: 28rpx;
  padding-top: 28rpx;
}
/* 支付 */
.iconStyle{
  width: 30rpx;
  height: 30rpx;
}
.title6 view{
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #181818;
  margin-bottom: 28rpx;
}
.title5 view{
  height: 80rpx;
  line-height: 80rpx;
  margin-bottom: 28rpx;
  text-align: right;
}
.backGroup{
  text-align: center;
}
.goBack{
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #0C6FF9;
  color: #fff;
  font-size: 28rpx;
}