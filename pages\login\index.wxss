/* pages/login/index.wxss */
page{
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.page{
  width: 100%;
  height: 100%;
}
.bg{
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: -1;
}
.title1{
  font-family: SourceHanSansCN;
  font-weight: 500;
  font-size: 67rpx;
  color: #fff;
  margin-top: 80rpx;
  margin-left: 62rpx;
}
.title2{
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  font-size: 44rpx;
  color: #fff;
  margin-top: 29rpx;
  margin-left: 62rpx;
}
.section{
  width: 650rpx;
  height: 528rpx;
  border-radius: 26rpx;
  margin-left: 50rpx;
  margin-top: 50rpx;
  background-color: #fff;
  padding-top: 50rpx;
}
.line{
  margin-left: 62rpx;
  margin-right: 62rpx;
  height: 110rpx;
}
.border{
  border-bottom: 1px solid #EBEBEB;
}
.iconStyle{
  width: 40rpx;
  height: 40rpx;
  float: left;
  margin-top: 35rpx;
  margin-right: 18rpx;
}
.iconStyle2{
  width: 30rpx;
  height: 30rpx;
  float: left;
  margin-top: 40rpx;
  margin-right: 18rpx;
}
.input{
  height: 110rpx;
}
.text{
  height: 110rpx;
  line-height: 110rpx;
  font-family: SourceHanSansCN;
  font-weight: 400;
  font-size: 28rpx;
  color: #B2B2B2;
}
.btn_login{
  height: 84rpx;
  line-height: 84rpx;
  background-color: #0B80E7 !important;
  border-radius: 10rpx;
  font-size: 36rpx !important;
  color: #FFFFFF;
  font-family: PingFang SC;
  margin-top: 30rpx;
}
.placeClass{
  font-size: 32rpx;
  color: #B2B2B2;
}