// pages/pay/index.js
var app=getApp();
import {https} from '../../utils/service'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id:'',// 核销通道id
    pay_channels:[],
    waitingCheckoutSum:0,// 应付
    waitingCheckoutAlreadypaid:0,// 已支付
    waitingCheckoutRemainingPayment:0,// 剩余支付
    ckh:'',// 参考号
    cost:0,// 消费额
    cardFlag: false,
    chooseCardFlag:false,
    goodsList:[],
    waterCode:'',
    payList:[], // 付款记录
    vipInfo:{
      c_tele2:'13900000001'
    },
  },
  input_price(e){
    let val=e.detail.value;
    this.setData({
      ckh:val
    })
  },
  input_price2(e){
    let val=e.detail.value;
    this.setData({
      cost:val
    })
  },
  // 提交搜索卡号
  btn_sure(){
    var that=this;
    let data={};
    data.cardno=that.data.card_no;
    data.mid=that.data.mid;
    let params={
      data:{
        "type": "card",
        data:data
      }
    }
    https(params).then((res)=>{
      console.log(res)
      if(res.data.length>0){
        that.setData({
          cardList:res.data,
          chooseCardFlag:true,
          cardFlag:false
        })
      }else{
        wx.showToast({
          title: '暂无结果',
          icon:'error'
        })
        setTimeout(() => {
          wx.navigateBack({
            delta: 1
          })
        }, 1500);
      }
      
    })
  },
  btn_submit(){
    var that=this;
    if(that.data.cost>that.data.waitingCheckoutRemainingPayment){
      wx.showModal({
        title: '提示',
        content: '消费额不能大于剩余付款金额',
        showCancel:false,
        complete: (res) => {
          if (res.cancel) {
          }
          if (res.confirm) {
          }
        }
      })
      return false
    }
    let data={};
    data.channel_type=that.data.id; //通道号id
    data.merchant_order_id=that.data.waterCode;// 流水号
    data.pay_code=that.data.ckh; // 参考号
    data.amount=parseFloat(that.data.cost);// 消费额
    data.total_amount=that.data.waitingCheckoutRemainingPayment;// 总金额
    data.mobile_no=that.data.vipInfo.c_tele2;// 会员电话
    let t_goods=[];
    that.data.goodsList.forEach((t,i)=>{
      t_goods.push({
        goodsId:t.c_gcode,
        goodsName:t.c_name,
        quantity:t.c_nowNumber,
        price:parseFloat(t.c_nowPrice),
        amount:t.c_nowSum
      })
    })
    data.goods=t_goods;
    data.payments=[];
    let params={
      data:{
        "type": "pay",
        data:data
      }
    }
    https(params).then((res)=>{
      console.log(res)
      if(res.status=='成功'){
        try {
          var value = wx.getStorageSync('payList');
          if (value) {
            that.setData({
              payList:value
            })
            that.data.payList.push({
              c_type:res.data.c_type,
              writeoff_amount:res.data.writeoff_amount,
              writeoff_time:res.data.writeoff_time,
              channel_type:res.data.channel_type.toString()
            })
            wx.setStorageSync('payList', that.data.payList);
            let msg='消费成功';
            wx.reLaunch({
              url: 'result?status='+res.status+'&msg='+msg,
            })
            // Do something with return value
          }else{
            let payList=[];
            payList.push({
              c_type:res.data.c_type,
              writeoff_amount:res.data.writeoff_amount,
              writeoff_time:res.data.writeoff_time,
              channel_type:res.data.channel_type.toString()
            })
            that.setData({
              payList:payList
            })
            wx.setStorageSync('payList', that.data.payList);
            let msg='消费成功';
            wx.reLaunch({
              url: 'result?status='+res.status+'&msg='+msg,
            })
          }
        } catch (e) {
          // Do something when catch error
        }
        // 计算剩余
        // app.calc_price();
      }else{
        let msg=res.message;
        wx.reLaunch({
          url: 'result?status='+res.status+'&msg='+msg,
        })
      }
    })
  },
  btn_choose(e){
    var that=this;
    let index=e.currentTarget.dataset.index;
    that.data.cardInfo=that.data.cardList[index];
    that.setData({
      cardInfo:that.data.cardInfo,
      chooseCardFlag:false
    })
  },
  btn_cancel(){
    this.setData({
      cardFlag:false,
      chooseCardFlag:false
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options)
    var that=this;
    that.setData({
      id:options.id
    })
    
    // 使用全局方法获取流水号
    app.validateAndGetWaterCode((waterCode) => {
      if(waterCode) {
        that.setData({
          waterCode: waterCode
        });
        console.log("银行卡支付页面流水号获取成功:", waterCode);
      } else {
        console.error("银行卡支付页面流水号获取失败");
      }
    });
    
    try {
      var value = wx.getStorageSync('waitingCheckoutList')
      if (value) {
        that.setData({
          goodsList:value
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('waitingCheckoutSum')
      if (value) {
        that.setData({
          waitingCheckoutSum:value
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('waitingCheckoutAlreadypaid')
      if (value) {
        that.setData({
          waitingCheckoutAlreadypaid:value
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('waitingCheckoutRemainingPayment')
      if (value) {
        that.setData({
          waitingCheckoutRemainingPayment:value
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('vipInfo')
      if (value) {
        that.setData({
          vipInfo:value
        })
      }
    } catch (e) {
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})