// pages/pay/coupons.js
var app=getApp();
import {https} from '../../utils/service'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    pay_channels:[],
    id:'',// 核销通道id
    subId:'',// 子id
    vipInfo:null
  },
  btn_choose(e){
    var that=this;
    let subId=e.currentTarget.dataset.id;
    if(that.data.vipInfo!=null&&subId=='5'){
      that.getCouponList();
      return false
    }
    wx.scanCode({
      success (res) {
        console.log(res)
          wx.navigateTo({
            url: 'couponsDetail?code='+res.result+'&id='+subId,
          })
      },fail(res){
        console.log(res);
          wx.navigateBack({
            delta: 1
          })
      }
    })
    
    // wx.navigateBack({
    //   delta: 1
    // })
  },
  getCouponList(){
    var that=this;
    let params={
      data:{
        "type": "coupon_list",
        data:{
          "phone_no":that.data.vipInfo.c_tele2
        }
      }
    }
    https(params).then((res)=>{
      console.log(res)
      wx.setStorageSync('couponsVipList', res.data);
      wx.redirectTo({
        url: 'couponsVipList',
      })
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    var that=this;
    that.setData({
      id:options.id
    })
    try {
      var value = wx.getStorageSync('pay_channels')
      if (value) {
        let sub=[];
        value.forEach((t,i) => {
          if(t.id=='3000'){
            sub=t.sub;
          }
        });
        that.setData({
          pay_channels:sub
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('vipInfo')
      if (value) {
        that.setData({
          vipInfo:value
        })
      }
    } catch (e) {
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    wx.hideHomeButton();  
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})