<!--pages/pay/index.wxml-->
<view class="page">
  <scroll-view scroll-y="true" style="height: 95vh;">
      <!-- <view wx:if="{{vipInfo == null}}" class="noVip">
        <text>当前结算未添加会员,一旦发起支付无法添加会员</text>
      </view> -->
      <view class="section">
        <view>
          <van-row>
            <van-col span="9" class="title1">{{waterCode}}</van-col>
            <van-col span="9" class="title1">{{time}}</van-col>
            <van-col span="6">
             <!-- <button wx:if="{{vipInfo == null}}" class="btn_addVip">添加会员</button> -->
             <view wx:if="{{vipInfo != null}}">
              <image class="iconStyle2" src="/images/pay/icon_user.png" mode=""/>
              <text class="title1">{{vipInfo.c_name}}</text>
             </view>
            </van-col>
          </van-row>
        </view>
          <!-- 商品列表 -->
        <view class="goods" wx:for="{{goodsList}}">
          <view class="goodsName">
            <text class="goodsDetail">{{item.c_name}}</text>
            <text class="goodsDetail goodsDetail2">{{item.c_gcode}}</text>
          </view>
          <view class="goodsNum">
            *{{item.c_nowNumber}}
          </view>
          <view class="goodsPrice">
            成交价:{{item.c_nowPrice}}
          </view>
          <view class="goodsSum">
            小计:{{item.c_nowSum}}
          </view>
        </view>
      </view>
      <!-- 价格明细 -->
      <view class="section">
        <view style="margin-top: 24rpx;">
          <van-row>
            <van-col span="12" class="title2">应付金额(元)：<text class="title3">{{waitingCheckoutSum}}</text></van-col>
            <van-col span="12" class="title2"></van-col>
          </van-row>
        </view>
        <view>
          <van-row>
            <van-col span="12" class="title2">已付金额(元)：<text class="title3">{{waitingCheckoutAlreadypaid}}</text></van-col>
            <van-col span="12" class="title2">剩余付款(元)：<text class="title3">{{waitingCheckoutRemainingPayment}}</text></van-col>
          </van-row>
        </view>
      </view>
       <!-- 付款明细 -->
       <view class="section">
        <view class="title4">付款明细</view>
        <view>
          <van-row wx:for="{{payList}}">
            <van-col span="12" class="title2">{{item.c_type}}支付：<text class="title3">{{item.writeoff_amount}}</text></van-col>
            <van-col span="12" class="title2">{{item.writeoff_time}}</van-col>
          </van-row>
        </view>
      </view>
       <!-- 支付方式 -->
       <view class="section">
        <view class="title4">支付</view>
        <view wx:for="{{pay_channels}}" data-id="{{item.id}}" data-index="{{index}}" bindtap="btn_link">
          <van-row>
            <van-col span="20" class="title6">{{item.name}}</van-col>
            <van-col span="4" class="title5">
              <image class="iconStyle" src="/images/pay/icon_back.png" mode=""/>
            </van-col>
          </van-row>
        </view>
       </view>
       <view class="backGroup">
          <button class="goBack"  bindtap="goBack">返回订单</button>
        </view>
    </scroll-view>
    
</view>