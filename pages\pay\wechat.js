// pages/pay/index.js
var app=getApp();
import {https} from '../../utils/service'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    fullDiscount:0,// 满减
    pay_channels:[],
    waitingCheckoutSum:0,// 应付
    waitingCheckoutAlreadypaid:0,// 已支付
    waitingCheckoutRemainingPayment:0,// 剩余支付
    xf:0,
    vipInfo:{
      c_tele2:'13900000001'
    },
    waterCode:'',
    channelsId:'',// 支付通道id
    payList:[],
  },
  input_price(e){
    let val=e.detail.value;
    this.setData({
      xf:val
    })
  },
  btn_submit(){
    var that=this;
    if(that.data.xf>that.data.waitingCheckoutRemainingPayment){
      wx.showModal({
        title: '提示',
        content: '消费额不能大于剩余付款金额',
        showCancel:false,
        complete: (res) => {
          if (res.cancel) {
          }
          if (res.confirm) {
          }
        }
      })
      return false
    }
    
    // 验证流水号是否存在
    app.validateAndGetWaterCode((waterCode) => {
      if(waterCode) {
        that.setData({
          waterCode: waterCode
        });
        // 执行支付逻辑
        that.executePayment();
      } else {
        wx.showToast({
          title: '获取流水号失败',
          icon: 'error'
        });
      }
    });
  },
  
  // 执行支付逻辑
  executePayment(){
    var that=this;
    let params={};
    wx.scanCode({
      success (res) {
        console.log(res)
        if(res.errMsg=="scanCode:ok"){
          params.channel_type=that.data.channelsId.toString(); //通道号 4000
          params.merchant_order_id=that.data.waterCode;// 流水号
          params.pay_code=res.result; //扫付款码拿到的 从二维码中
          params.amount=that.data.xf;
          params.total_amount=that.data.waitingCheckoutRemainingPayment;
          params.mobile_no=that.data.vipInfo.c_tele2;// 会员电话
          params.goods=[];
          params.payments=[];
          let content={
            data:{
              "type": "pay",
              data:params
            }
          }
          https(content).then((res)=>{
            console.log(res)
            if(res.status=='成功'){
              // wx.setStorageSync('xf', this.data.mz);
                try {
                  var value = wx.getStorageSync('payList');
                  if (value) {
                    that.setData({
                      payList:value
                    })
                    that.data.payList.push({
                      c_type:res.data.c_type,
                      writeoff_amount:res.data.writeoff_amount,
                      writeoff_time:res.data.writeoff_time,
                      channel_type:res.data.channel_type
                    })
                    wx.setStorageSync('payList', that.data.payList);
                    let msg='消费成功';
                    wx.reLaunch({
                      url: 'result?status='+res.status+'&msg='+msg,
                    })
                    // Do something with return value
                  }else{
                    let payList=[];
                    payList.push({
                      c_type:res.data.c_type,
                      writeoff_amount:res.data.writeoff_amount,
                      writeoff_time:res.data.writeoff_time,
                      channel_type:res.data.channel_type
                    })
                    that.setData({
                      payList:payList
                    })
                    wx.setStorageSync('payList', that.data.payList);
                    let msg='消费成功';
                    wx.reLaunch({
                      url: 'result?status='+res.status+'&msg='+msg,
                    })
                  }
                } catch (e) {
                  // Do something when catch error
                }
                // 计算剩余
                // app.calc_price();
            }else{
              let msg=res.message;
              wx.reLaunch({
                url: 'result?status='+res.status+'&msg='+msg,
              })
            }
          })
          // 拿到res.result   作为核销接口里面的pay_code参数
          // 用会员中的c_tele2作为mobile_no
        }
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options)
    let id=options.id;
    var that=this;
    that.setData({
      channelsId:id
    })
    try {
      var value = wx.getStorageSync('waitingCheckoutList')
      if (value) {
        that.setData({
          goodsList:value
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('waitingCheckoutSum')
      if (value) {
        that.setData({
          waitingCheckoutSum:value
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('waitingCheckoutAlreadypaid')
      if (value) {
        that.setData({
          waitingCheckoutAlreadypaid:value
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('waitingCheckoutRemainingPayment')
      if (value) {
        that.setData({
          waitingCheckoutRemainingPayment:value,
          xf:value
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('vipInfo')
      if (value) {
        that.setData({
          vipInfo:value
        })
      }
    } catch (e) {
    }
    
    // 使用全局方法获取流水号
    app.validateAndGetWaterCode((waterCode) => {
      if(waterCode) {
        that.setData({
          waterCode: waterCode
        });
        console.log("微信支付页面流水号获取成功:", waterCode);
      } else {
        console.error("微信支付页面流水号获取失败");
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    wx.hideHomeButton();  
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})