var app=getApp();
import {https} from '../../utils/service'
Page({
  /**
   * 页面的初始数据
   */
  data: {
    active: 2,
    vipInfo:null,// 会员信息
    day_amount:0,
    week_amount:0,
    employeeInfo:{},
  },
 
 // tabbar
 onChange(e) {
  console.log(e)
  let index=e.detail;
  if(index==0){
    wx.reLaunch({
      url: '../index/index',
    })
  }
  if(index==1){
    wx.reLaunch({
      url: '../waitingSettlement/index',
    })
  }
},
  btn_link(e){
    var that=this;
    let index=e.currentTarget.dataset.index;
    if(index==0){
      wx.navigateTo({
        url: 'record?id='+index,
      })
    }
    if(index==1){
      wx.navigateTo({
        url: 'record?id='+index,
      })
    }
    if(index==4){
      wx.navigateTo({
        url: 'feedback',
      })
    }
    if(index==5){
      wx.navigateTo({
        url: 'setting',
      })
    }
  },
  
 
 
  
  // 会员搜索
  vipBtn_search(){
    var that=this;
    console.log(that.data.vipSearchText);
    if(that.data.vipSearchText==''){
      wx.showToast({
        title: '输入为空',
        icon:'error'
      })
      return false
    }
    let params={
      data:{
        "type": "cust",
        data:{
          "cardno":that.data.vipSearchText
        }
      }
    }
    https(params).then((res)=>{
      console.log(res)
      that.setData({
        vipList:res.data
      })
    })
  },
  // 获取营业额
  getSales(){
    var that=this;
    let params={
      data:{
        "type": "amount",
        data:{}
      }
    }
    https(params).then((res)=>{
      console.log(res)
      that.setData({
        day_amount:res.data.day_amount,
        week_amount:res.data.week_amount
      })
    })
  },
    // 获取收银员信息
    getEmployeeInfo(){
      var that=this;
      let params={
        data:{
          "type": "employee",
          data:{}
        }
      }
      https(params).then((res)=>{
        console.log(res)
        that.setData({
          employeeInfo:res.data
        })
      })
    },
 
 
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    var that=this;
    this.getSales();
    this.getEmployeeInfo();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})