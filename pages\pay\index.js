// pages/pay/index.js
var app=getApp();
import {https} from '../../utils/service'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    goodsList:[],
    vipInfo:null,
    waterCode:'',
    time:'',
    pay_channels:[],
    payList:[
      // {
      //   id:'4000',
      //   name:"微信/支付宝",
      //   value:900,
      //   time:'20240710'
      // },
      // {
      //   id:'4000',
      //   name:"微信/支付宝",
      //   value:900,
      //   time:'20240710'
      // }
    ],// 付款明细
    waitingCheckoutSum:0,// 应付
    waitingCheckoutAlreadypaid:0,// 已支付
    waitingCheckoutRemainingPayment:0,// 剩余支付
  },
  // 会员搜索
  payWay_search(){
    var that=this;
    let params={
      data:{
        "type": "pay_channels",
        data:{}
      }
    }
    https(params).then((res)=>{
      console.log(res)
      that.setData({
        pay_channels:res.data
      })
      wx.setStorageSync('pay_channels', that.data.pay_channels);
    })
  },
  btn_link(e){
    var that=this;
    let index=e.currentTarget.dataset.index;
    let id=e.currentTarget.dataset.id;
    if(id=='1000'){
      wx.navigateTo({
        url: 'valueCard',
      })
    }
    if(id=='2000'){
      let f=0;
      that.data.payList.forEach((t,i)=>{
        if(t.channel_type=='2000'){
          f=1;
        }
      })
      if(f==0){
        wx.navigateTo({
          url: 'discount',
        })
      }else{
        wx.showToast({
          title: '仅能满减一次',
          icon:'error'
        })
      }
    }
    if(id=='3000'){
      wx.navigateTo({
        url: 'coupons?id='+id,
      })
    }
    if(id=='4000'){
      wx.navigateTo({
        url: 'wechat?id='+id,
      })
    }
    if(id=='5000'){
      wx.navigateTo({
        url: 'bankCardList?id='+id,
      })
    }
  },
  goBack(){
    var that=this;
    if(that.data.goodsList.length>0){
      wx.redirectTo({
        url: '../waitingSettlement/index',
      })
    }else{
      wx.redirectTo({
        url: '../index/index',
      })
    }
   
  },
  initPage(){
    var that=this;
    try {
      var value = wx.getStorageSync('waitingCheckoutList')
      if (value) {
        that.setData({
          goodsList:value
        })
      }
    } catch (e) {
    }
    
    // 使用全局方法获取流水号
    app.validateAndGetWaterCode((waterCode) => {
      if(waterCode) {
        that.setData({
          waterCode: waterCode
        });
        console.log("支付页面流水号获取成功:", waterCode);
      } else {
        console.error("支付页面流水号获取失败");
      }
    });
    
    try {
      var value = wx.getStorageSync('waitingCheckoutTime')
      if (value) {
        that.setData({
          time:value
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('vipInfo')
      if (value) {
        that.setData({
          vipInfo:value
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('waitingCheckoutSum')
      if (value) {
        that.setData({
          waitingCheckoutSum:value
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('waitingCheckoutAlreadypaid')
      if (value) {
        that.setData({
          waitingCheckoutAlreadypaid:value
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('waitingCheckoutRemainingPayment')
      if (value) {
        that.setData({
          waitingCheckoutRemainingPayment:value
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('payList')
      if (value) {
        that.setData({
          payList:value
        })
      }
    } catch (e) {
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    var that=this;
    
    that.payWay_search();
    that.initPage();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    var that=this;
    wx.hideHomeButton();  
      app.calc_price();
      that.initPage();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})