<view>
  <view wx:if="{{type==1}}">
    <van-cell title="选择日期区间" value="{{ date }}" bind:click="onDisplay" />
  </view>
  <view class="section" wx:for="{{listData}}" wx:key="{{item.c_id}}">
    <!-- <view class="title">付款明细</view> -->
    <view class="list">
      <van-row>
        <van-col span="16" class="title">流水号：{{item.c_id}}</van-col>
        <van-col span="8" class="title">收银机：{{item.c_computer_id}}</van-col>
      </van-row>
      <van-row>
        <van-col span="16" class="title">时间：{{item.c_datetime}}</van-col>
        <van-col span="8" class="title">门店：{{item.c_store_id}}</van-col>
      </van-row>
    </view>
    <view class="list" wx:for="{{item.sg}}">
      <van-row>
        <van-col span="12" class="title">编号：{{item.c_gcode}}</van-col>
        <van-col span="24" class="title">商品：{{item.c_name}}</van-col>
      </van-row>
      <van-row>
        <van-col span="8" class="title">原价：{{item.c_price}}</van-col>
        <van-col span="8" class="title">现价：{{item.c_price_pro}}</van-col>
        <van-col span="8" class="title">数量：{{item.c_qtty}}</van-col>
      </van-row>
      <van-row>
        <van-col span="12" class="title">金额：{{item.c_amount}}</van-col>
        <van-col span="12" class="title">积分：{{item.c_score}}</van-col>
      </van-row>
    </view>
    <view class="list2" wx:for="{{item.sm}}">
      <van-row>
        <van-col span="12" class="title">付款方式：{{item.c_type}}</van-col>
        <van-col span="12" class="title">金额：{{item.c_amount}}</van-col>
      </van-row>
      <van-row wx:if="{{item.c_cardno}}">
        <van-col span="24" class="title">储值卡号：{{item.c_cardno}}</van-col>
      </van-row>
    </view>
  </view>
  <van-calendar
    show="{{ show }}"
    type="range"
    bind:close="onClose"
    bind:confirm="onConfirm"
    show-confirm="{{ false }}" 
    min-date="{{ minDate }}"
  />
</view>
