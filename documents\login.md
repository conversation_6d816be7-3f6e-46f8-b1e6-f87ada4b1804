# 登录页 (login) 文档

## 1. 页面概述

登录页是用户进入云POS小程序的第一步，负责用户的身份验证。用户需要输入手机号和密码进行登录，并可以选择记住密码，以便下次快速登录。

## 2. 文件结构

- <mcfile name="index.js" path="e:\miniprogram\yunpos\pages\login\index.js"></mcfile>: 页面逻辑文件，处理用户输入、登录请求和状态管理。
- <mcfile name="index.json" path="e:\miniprogram\yunpos\pages\login\index.json"></mcfile>: 页面配置文件，定义导航栏样式。
- <mcfile name="index.wxml" path="e:\miniprogram\yunpos\pages\login\index.wxml"></mcfile>: 页面结构文件，负责UI布局和数据绑定。
- <mcfile name="index.wxss" path="e:\miniprogram\yunpos\pages\login\index.wxss"></mcfile>: 页面样式文件，定义页面元素的样式。

## 3. 页面配置 (<mcfile name="index.json" path="e:\miniprogram\yunpos\pages\login\index.json"></mcfile>)

```json
{
  "navigationBarBackgroundColor": "#0C6FF9",
  "navigationBarTextStyle": "white",
  "navigationBarTitleText": "",
  "backgroundColor": "#0C6FF9",
  "backgroundTextStyle": "light",
  "usingComponents": {}
}
```

- `navigationBarBackgroundColor`: 导航栏背景颜色设置为深蓝色。
- `navigationBarTextStyle`: 导航栏标题文字颜色为白色。
- `navigationBarTitleText`: 导航栏标题为空，意味着页面顶部没有显示标题文字。

## 4. 页面逻辑 (<mcfile name="index.js" path="e:\miniprogram\yunpos\pages\login\index.js"></mcfile>)

### 4.1. 页面数据 (`data`)

```javascript
data: {
  phone: '', // 手机号输入框内容
  password: '', // 密码输入框内容
  rememberPasswordFlag: false, // 是否记住密码的标志
},
```

### 4.2. 页面方法

- `input_phone(e)`: 手机号输入框的输入事件，更新 `phone` 数据。
- `input_password(e)`: 密码输入框的输入事件，更新 `password` 数据。
- `rememberPassword(e)`: 记住密码选择框的点击事件，切换 `rememberPasswordFlag` 状态，并同步更新本地缓存。
- `btn_login()`: 登录按钮点击事件，发送登录请求到后端。
    - 使用 `wx.request` 发送POST请求到 `app.globalData.baseUrl + 'auth/token'`。
    - 请求成功（状态码200）后，将 `access_token` 和 `refresh_token` 存储到本地缓存和 `app.globalData`。
    - 如果选择了记住密码，将手机号和加密后的密码存储到本地缓存。
    - 登录成功后，跳转到首页 (`../index/index`)。
    - 登录失败（状态码非200）则显示“密码错误”的Toast提示。

### 4.3. 生命周期

- `onLoad(options)`: 页面加载时触发。
    - 从 `app.globalData` 获取 `rememberPasswordFlag` 状态并设置到页面数据。
    - 如果 `rememberPasswordFlag` 为 `true`，则尝试从本地缓存获取之前保存的手机号和加密密码，并填充到输入框。
    - 如果 `rememberPasswordFlag` 为 `false`，则清除本地缓存中的密码。

## 5. 页面结构 (<mcfile name="index.wxml" path="e:\miniprogram\yunpos\pages\login\index.wxml"></mcfile>)

页面主要包含以下元素：

- **背景图片**: 全屏的登录背景图。
- **欢迎语**: “您好！”和“欢迎来到泰富百货收银”。
- **登录表单区域**: 
    - 手机号输入框，带有图标和占位符。
    - 密码输入框，带有图标和占位符，密码隐藏显示。
    - “记住密码”选择框，通过 `rememberPasswordFlag` 控制图标显示。
    - “登录”按钮。

## 6. 样式 (<mcfile name="index.wxss" path="e:\miniprogram\yunpos\pages\login\index.wxss"></mcfile>)

样式文件定义了登录页面的布局和视觉效果，包括：

- 全局页面样式 (`page`, `.page`, `.bg`)
- 标题文字样式 (`.title1`, `.title2`)
- 登录表单区域样式 (`.section`, `.line`, `.border`)
- 输入框和图标样式 (`.iconStyle`, `.iconStyle2`, `.input`, `.placeClass`)
- “记住密码”文字样式 (`.text`)
- 登录按钮样式 (`.btn_login`)

## 7. 数据流程与实现方法

1.  **用户输入**: 用户在手机号和密码输入框中输入信息，通过 `input_phone` 和 `input_password` 方法实时更新页面数据。
2.  **记住密码**: 用户点击“记住密码”区域，`rememberPassword` 方法会切换 `rememberPasswordFlag` 的状态，并使用 `wx.setStorageSync` 将状态保存到本地，同时根据状态决定是否保存手机号和加密密码。
3.  **登录请求**: 用户点击“登录”按钮，`btn_login` 方法被调用，构造包含用户名和密码的请求体，通过 `wx.request` 发送POST请求到后端认证接口。
4.  **响应处理**: 
    - 如果后端返回成功（状态码200），则解析响应数据，提取 `access_token` 和 `refresh_token`，并将其存储到全局数据和本地缓存。随后调用 `app.getStore()` 获取商店信息，并跳转到首页。
    - 如果后端返回失败，则显示错误提示。
5.  **自动填充**: 页面加载时，如果 `rememberPasswordFlag` 为 `true`，则从本地缓存读取之前保存的手机号和加密密码，自动填充到输入框中，提升用户体验。

## 8. 注意事项

- **密码加密存储**: 密码在本地缓存中使用了 `encrypt: true` 进行加密存储，提高了安全性。
- **Token管理**: `access_token` 和 `refresh_token` 的管理是登录认证的关键，需要确保其安全存储和有效使用。
- **错误提示**: 登录失败时，通过 `wx.showToast` 给予用户明确的错误提示。
- **AppID配置**: 确保 `project.config.json` 中的 `appid` 配置正确，否则可能无法正常预览或发布。
- **网络请求**: 小程序的所有网络请求域名需要在微信公众平台进行配置，否则会报错。
- **API接口**: 后端API接口的联调请参考 <mcfile name="utils/service.js" path="e:\miniprogram\yunpos\utils\service.js"></mcfile> 文件中的定义和调用方式。