/* pages/pay/index.wxss */
page{
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #FAF8F5;
}
.page{
  width: calc(100% - 36rpx);
  height: 100%;
  padding-left: 18rpx;
  padding-right: 18rpx;
}
.section{
  background-color: #fff;
  padding: 28rpx 28rpx;
  border-radius: 14rpx;
  margin-bottom: 18rpx;
}
.btn_addVip{
  width: 150rpx;
  height: 58rpx;
  background: #0C72FF;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #FFFFFF;
}
.iconStyle2{
  width: 30rpx;
  height: 30rpx;
  vertical-align: middle;
}

/* 模块2 价目表 */
.title2 view{
  font-size: 24rpx;
  color: #181818;
  margin-bottom: 24rpx;
}
.title3{
  font-size: 24rpx;
  color: #FF1A1A
}
.title4{
  font-size: 28rpx;
  color: #3A3A3A;
  height: 80rpx;
  line-height: 80rpx;
  display: inline-block;
  vertical-align: middle;
}
.iconStyle{
  width: 44rpx;
  height: 44rpx;
  vertical-align: middle;
  margin-right: 6rpx;
}
.formStyle1TitleInput{
  width: 420rpx;
  height: 80rpx;
  background: #EEEEEE;
  border-radius: 8rpx;
  border: 1px solid #DCDCDC;
  display: inline-block;
  vertical-align: middle;
  padding-left: 24rpx;
}
.btn_submit{
  width: 188rpx;
  height: 60rpx;
  line-height: 60rpx;
  background: #0C72FF;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #FFFFFF;
  margin-top: 18rpx;
}