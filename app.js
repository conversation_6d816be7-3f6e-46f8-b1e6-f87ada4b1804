// app.js
import {https} from './utils/service'
App({
  getCurrentTime() {  
    let now = new Date();  
    let year = now.getFullYear(); // 年  
    let month = (now.getMonth() + 1).toString().padStart(2, '0'); // 月，月份是从0开始的，所以要+1  
    let day = now.getDate().toString().padStart(2, '0'); // 日  
    let hour = now.getHours().toString().padStart(2, '0'); // 时  
    let minute = now.getMinutes().toString().padStart(2, '0'); // 分  
    // let second = now.getSeconds().toString().padStart(2, '0'); // 秒  
    // 拼接成需要的格式  
    // let currentTime = `${year}-${month}-${day} ${hour}:${minute}:${second}`;  
    let currentTime = `${year}-${month}-${day} ${hour}:${minute}`;  
    // 返回格式化后的时间字符串  
    return currentTime;  
  },  
  getCurrentTime2() {  
    let now = new Date();  
    let year = now.getFullYear(); // 年  
    let month = (now.getMonth() + 1).toString().padStart(2, '0'); // 月，月份是从0开始的，所以要+1  
    let day = now.getDate().toString().padStart(2, '0'); // 日  
    let hour = now.getHours().toString().padStart(2, '0'); // 时  
    let minute = now.getMinutes().toString().padStart(2, '0'); // 分  
    let second = now.getSeconds().toString().padStart(2, '0'); // 秒  
    // 拼接成需要的格式  
    let currentTime = `${year}-${month}-${day} ${hour}:${minute}:${second}`;  
    // 返回格式化后的时间字符串  
    return currentTime;  
  },  
  getAccessToken(){
    var that=this;
    wx.request({
      url: that.globalData.baseUrl+'auth/refresh_token', 
      data: {
        "refresh_token": that.globalData.refresh_token,
      },
      method:"POST",
      header: {
        'content-type': 'application/json;charset=UTF-8'
      },
      success (res) {
        try {
          console.log(res.data)
          wx.setStorageSync('access_token', res.data.access_token);
          wx.setStorageSync('refresh_token', res.data.refresh_token);
          that.globalData.access_token=res.data.access_token;
          that.globalData.refresh_token=res.data.refresh_token;
        } catch (e) { 
          console.log(e)
        }
      }
    })
  },
  clearOrder(){
    try {
      wx.removeStorageSync('waterCode');
      wx.removeStorageSync('waitingCheckoutList');
      wx.removeStorageSync('waitingCheckoutTime');
      wx.removeStorageSync('waitingCheckoutSum');
      wx.removeStorageSync('waitingCheckoutAlreadypaid');
      wx.removeStorageSync('waitingCheckoutRemainingPayment');
      wx.removeStorageSync('vipInfo');
      wx.removeStorageSync('pay_channels');
      wx.removeStorageSync('payList');
    } catch (e) {
      // Do something when catch error
    }
  },
  // 计算剩余金额
  calc_price(){
    try {
      var value = wx.getStorageSync('payList')
      var waitingCheckoutSum = wx.getStorageSync('waitingCheckoutSum')
      if (value) {
        let sum=0;
        value.forEach((t,i)=>{
          sum+=t.writeoff_amount;
          sum=parseFloat(sum.toFixed(2));
        })
        let waitingCheckoutAlreadypaid=sum;
        let waitingCheckoutRemainingPayment= waitingCheckoutSum - waitingCheckoutAlreadypaid ;
        waitingCheckoutRemainingPayment=parseFloat(waitingCheckoutRemainingPayment.toFixed(2));
        wx.setStorageSync('waitingCheckoutAlreadypaid', waitingCheckoutAlreadypaid);
        wx.setStorageSync('waitingCheckoutRemainingPayment', waitingCheckoutRemainingPayment);
        if(waitingCheckoutRemainingPayment<=0){
          this.cancelOrder()
        }
      }
    } catch (e) {
      // Do something when catch error
    }
    
  },
  // 订单结束-完成
  cancelOrder(){
    var that=this;
    let data=[];
    try {
      let waitingCheckoutList=wx.getStorageSync('waitingCheckoutList');
      let payList=wx.getStorageSync('payList');
      let waterCode=wx.getStorageSync('waterCode');
      
      // 如果没有流水号，先获取流水号，然后再执行订单完成逻辑
      if(!waterCode){
        let params={
          data:{
            "type": "sn"
          }
        }
        https(params).then((res)=>{
          waterCode = res.data.serial_number;
          wx.setStorageSync('waterCode', waterCode);
          // 获取到流水号后，执行订单完成逻辑
          that.executeOrderFinish(waterCode, waitingCheckoutList, payList);
        }).catch(err => {
          console.error('获取流水号失败:', err);
          wx.showModal({
            title: '提示',
            content: '获取流水号失败，请重试',
            showCancel:false
          });
        });
        return; // 等待异步获取流水号完成
      }
      
      // 如果有流水号，直接执行订单完成逻辑
      that.executeOrderFinish(waterCode, waitingCheckoutList, payList);
      
    } catch (error) {
      console.log(error)
    }
  },

  // 执行订单完成逻辑
  executeOrderFinish(waterCode, waitingCheckoutList, payList){
    var that=this;
    let data=[];
    
    let vipInfo={
      c_cardno:''
    };
    try {
      var value = wx.getStorageSync('vipInfo')
      if (value) {
          vipInfo=value
      }
    } catch (e) {
    }
    
    if(waitingCheckoutList){
      waitingCheckoutList.forEach((t,i)=>{
        data.push({
          'c_id': waterCode,
          'c_flag':'G',
          'c_cardno': vipInfo.c_cardno,
          'c_in_code': t.c_in_code,
          'c_adno': t.c_adno,
          'c_gcode': t.c_gcode,
          'c_subcode': t.c_subcode,
          'c_price': t.c_price,
          'c_price_pro': parseFloat(t.c_nowPrice),
          'c_price_disc': parseFloat(t.c_nowPrice),
          'c_qtty': t.c_nowNumber,
          'c_amount':t.c_nowSum,
          'c_gds_type': t.c_type,
          'c_pro_status': t.c_pro_status
        })
      })
      console.log(payList);
      payList.forEach((t,i)=>{
        let cardno='';
        if(t.channel_type.length<4){// 电子券
          cardno=t.pay_code+'_'+t.writeoff_amount;
          if(t.channel_type=='28'){
            // 企微支付
            cardno=t.pay_code
          }
        }
        if(t.channel_type=='1000'){// 储值卡
          cardno=t.c_cardno;
        }
        data.push({
          'c_id': waterCode,
          'c_flag':'M',
          'c_cardno': cardno,
          'c_price': 0,
          'c_price_pro': 0,
          'c_price_disc': 0,
          'c_qtty':0,
          'c_amount':t.writeoff_amount,
          'channel_type': t.channel_type.toString()
        })
      })
      console.log(data);
    }else{
    }
    
    let params={
      data:{
        "type": "finish",
        data:data
      }
    }
    wx.showLoading({
      title: '请稍后',
    })
    https(params).then((res)=>{
      console.log(res)
      wx.hideLoading()
      if(res.status=='成功'){
        wx.showModal({
          title: '提示',
          content: '订单已完成',
          showCancel:false,
          complete: (res) => {
            if (res.cancel) {
              that.clearOrder();
              setTimeout(() => {
                wx.reLaunch({
                  url: '/pages/index/index',
                })
              }, 1000);
            }
            if (res.confirm) {
              that.clearOrder();
              setTimeout(() => {
                wx.reLaunch({
                  url: '/pages/index/index',
                })
              }, 1000);
            }
          }
        })
       
      }else{
        wx.showModal({
          title: '提示',
          content: '订单完成失败',
          showCancel:false,
          complete: (res) => {
            if (res.cancel) {
              wx.reLaunch({
                url: '/pages/pay/index',
              })
            }
            if (res.confirm) {
              wx.reLaunch({
                url: '/pages/pay/index',
              })
            }
          }
        })
      }
      
    })
  },

  // 获取门店信息
  getStore() {  
    var that=this;
    let params={
      data:{
        "type": "store",
      }
    }
    https(params).then((res)=>{
      that.globalData.storeInfo=res.data;
      wx.setStorageSync('storeInfo', res.data);
    })
  },
  
  // 验证并获取流水号
  validateAndGetWaterCode(callback) {
    var that=this;
    let waterCode = wx.getStorageSync('waterCode');
    
    if(!waterCode || waterCode.trim() === ''){
      // 流水号为空，重新获取
      let params={
        data:{
          "type": "sn"
        }
      }
      https(params).then((res)=>{
        waterCode = res.data.serial_number;
        wx.setStorageSync('waterCode', waterCode);
        that.globalData.waterCode = waterCode;
        if(callback) callback(waterCode);
      }).catch(err => {
        console.error('获取流水号失败:', err);
        wx.showToast({
          title: '获取流水号失败',
          icon: 'error'
        });
        if(callback) callback(null);
      });
    } else {
      // 流水号存在，直接返回
      if(callback) callback(waterCode);
    }
  },
  onLaunch() {
    // 记住密码
    try {
      let val=wx.getStorageSync('rememberPasswordFlag');
      let token=wx.getStorageSync('access_token');
      this.globalData.refresh_token=wx.getStorageSync('refresh_token');
      if(val){
        this.globalData.rememberPasswordFlag=val;
      }else{
        this.globalData.rememberPasswordFlag=false;
      }
      if(token){
        this.globalData.access_token=token;
      }else{
        wx.reLaunch({
          url: '/pages/login/index',
        })
      }
    } catch (error) {
      console.log(error)
    }
    
    // 展示本地存储能力
    // const logs = wx.getStorageSync('logs') || []
    // logs.unshift(Date.now())
    // wx.setStorageSync('logs', logs)

    // 登录
    // wx.login({
    //   success: res => {
    //     // 发送 res.code 到后台换取 openId, sessionKey, unionId
    //   }
    // })
  },
  globalData: {
    baseUrl:'https://yunpos.cztfjt.cn/',
    // baseUrl:'http://47.116.195.159:9797/',
    loginUrl:'',
    userInfo: null,
    rememberPasswordFlag:false,
    access_token:'',
    refresh_token:'',
    waterCode:'', // 流水号
    storeInfo:{},// 门店信息
  }
})
