// pages/login/index.js
var app=getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    phone:'',
    password:'',
    // phone:'13401604610',
    // password:'604610',
    rememberPasswordFlag:false,
  },
  input_phone(e){
    let val=e.detail.value;
    var that=this;
    that.data.phone=val;
  },
  input_password(e){
    let val=e.detail.value;
    var that=this;
    that.data.password=val;
  },
  rememberPassword(e){
    var that=this;
    let status=e.currentTarget.dataset.status;
    console.log(status)
    if(status==1){
      that.setData({
        rememberPasswordFlag:false
      })
      try {
        wx.setStorageSync('rememberPasswordFlag', false)
      } catch (e) { 
        console.log(e)
      }
    }else{
      that.setData({
        rememberPasswordFlag:true
      })
      try {
        wx.setStorageSync('rememberPasswordFlag', true)
      } catch (e) { 
        console.log(e)
      }
    }
   
  },
  btn_login(){
    var that=this;
    wx.request({
      url: app.globalData.baseUrl+'auth/token', //仅为示例，并非真实的接口地址
      data: {
        "username": that.data.phone,
        "password": that.data.password
      },
      method:"POST",
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      success (res) {
        console.log(res)
        if(res.statusCode=='200'){
          console.log(res.data.access_token)
          setTimeout(() => {
            wx.setStorageSync('access_token', res.data.access_token);
            wx.setStorageSync('refresh_token', res.data.refresh_token);
            app.globalData.access_token=res.data.access_token;
            app.globalData.refresh_token=res.data.refresh_token;
            app.getStore();
          }, 100);
          if(that.data.rememberPasswordFlag==true){
            wx.setStorageSync('phone', that.data.phone);
            wx.setStorage({
              key: "pd",
              data: that.data.password,
              encrypt: true, // 若开启加密存储，setStorage 和 getStorage 需要同时声明 encrypt 的值为 true
              success() {
              
              }
            })
          }
          setTimeout(() => {
            wx.reLaunch({
              url: '../index/index',
            })
          }, 500);
        }else{
          wx.showToast({
            title: '密码错误',
            icon:'error'
          })
        }
          
        
       
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    var that=this;
    let  t_rememberPasswordFlag=app.globalData.rememberPasswordFlag;
    that.setData({
      rememberPasswordFlag:t_rememberPasswordFlag
    })
    if(that.data.rememberPasswordFlag==true){
      try {
        var value = wx.getStorageSync('phone')
        if (value) {
          that.setData({
            phone:value
          })
        }
      } catch (e) {
      }
      wx.getStorage({
        key: "pd",
        encrypt: true, // 若开启加密存储，setStorage 和 getStorage 需要同时声明 encrypt 的值为 true
        success(res) {
          that.setData({
            password:res.data
          })
        }
      })
    }else{
      try {
        wx.removeStorageSync('pd')
      } catch (e) {
        console.log(e)
      }
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})