<!--pages/pay/index.wxml-->
<view class="page">
  <!-- 价格明细 -->
  <view class="section">
    <view>
      <van-row>
        <van-col span="12" class="title2">应付金额(元)：<text class="title3">{{waitingCheckoutSum}}</text></van-col>
        <van-col span="12" class="title2"></van-col>
      </van-row>
    </view>
    <view>
      <van-row>
        <van-col span="12" class="title2">满减活动(元)：<text class="title3">{{fullDiscount}}</text></van-col>
        <van-col span="12" class="title2"></van-col>
      </van-row>
    </view>
    <view>
      <van-row>
        <van-col span="12" class="title2">已付金额(元)：<text class="title3">{{waitingCheckoutAlreadypaid}}</text></van-col>
        <van-col span="12" class="title2">剩余付款(元)：<text class="title3">{{waitingCheckoutRemainingPayment}}</text></van-col>
      </van-row>
    </view>
  </view>
  
    <!-- 支付方式 -->
    <view class="section">
      <view style="margin-bottom: 22rpx;">
        <image class="iconStyle" src="/images/pay/icon_ky.png" mode=""/>
        <text class="title4" style="margin-right: 26rpx;">余额（元）</text>
        <input class="formStyle1TitleInput"  type="digit" value="{{cardInfo.deposit}}" disabled  bindinput="input_price2" />
      </view>
      <view>
        <image class="iconStyle" src="/images/pay/icon_xf.png" mode=""/>
        <text class="title4">消费额（元）</text>
        <input class="formStyle1TitleInput2"  type="digit" value="{{xf}}"  bindinput="input_price" />
      </view>
    </view>
    <view>
      <button class="btn_submit" bindtap="btn_submit">确定</button>
    </view>
</view>
<!-- 输入卡号 -->
<van-popup show="{{ cardFlag }}" 
custom-style="width: 480rpx;height: 300rpx;background: #FFFFFF;border-radius: 10rpx;">
  <view style="padding: 36rpx;">
    <view class="title1">请输入卡识别码</view>
      <input class="cardInput" type="number" value="{{card_no}}" bindinput="cardInput" placeholder="请输入" placeholder-class="placeClass" />
  </view>
  <view class="btn_group">
    <view class="btn_cancel" bindtap="btn_cancel">取消</view>
    <view class="btn_sure" bindtap="btn_sure">确定</view>
  </view>
</van-popup>

<!-- 选卡 -->
<van-popup show="{{ chooseCardFlag }}" 
custom-style="width: 680rpx;height: 500rpx;background: #FFFFFF;border-radius: 10rpx;">
  <view style="padding: 36rpx;">
    <view class="chooseTitle">点击选择储值卡</view>
    <van-row class="cardItem" wx:for="{{cardList}}" >
      <view bindtap="btn_choose" data-index="{{index}}">
        <van-col span="18" class="title2">名称：{{item.card_name}}</van-col>
        <van-col span="6" class="title2">{{item.deposit}}元</van-col>
      </view>
    </van-row>
  </view>
</van-popup>