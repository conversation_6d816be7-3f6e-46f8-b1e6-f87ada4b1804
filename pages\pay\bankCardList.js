Page({

  /**
   * 页面的初始数据
   */
  data: {
    pay_channels:[],
    id:'',// 核销通道id
    subId:'',// 子id
  },
  btn_choose(e){
    var that=this;
    let subId=e.currentTarget.dataset.id;
    wx.navigateTo({
      url: 'bankCard?id='+subId,
    })
    
    // wx.navigateBack({
    //   delta: 1
    // })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    var that=this;
    that.setData({
      id:options.id
    })
    try {
      var value = wx.getStorageSync('pay_channels')
      if (value) {
        let sub=[];
        value.forEach((t,i) => {
          if(t.id=='5000'){
            sub=t.sub;
          }
        });
        
        that.setData({
          pay_channels:sub
        })
      }
    } catch (e) {
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    wx.hideHomeButton();  
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})