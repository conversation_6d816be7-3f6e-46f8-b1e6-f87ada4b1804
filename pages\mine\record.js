var app=getApp();
import {https} from '../../utils/service'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    listData:[],
    type:'', //0当日,1时间段
    show: false,
    date: '',
    start_date:'',
    end_date:'',
    minDate: new Date(2024, 0, 1).getTime(),
  },
  onDisplay() {
    this.setData({ show: true });
  },
  onClose() {
    this.setData({ show: false });
  },
  formatDate(date) {
    console.log(date)
    date = new Date(date);
    return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
  },
  onConfirm(event) {
    const [start, end] = event.detail;
    this.setData({
      show: false,
      date: `${this.formatDate(start)} ~ ${this.formatDate(end)}`,
      start_date:`${this.formatDate(start)}`,
      end_date:`${this.formatDate(end)}`
    });
    this.getOrder();
  },
   // 获取订单详情
  getOrder(){
    var that=this;
    let type=this.data.type;
    let data={};
    if(type==1){
      data.start_date=that.data.start_date,
      data.end_date=that.data.end_date
    }
    // data.start_date='2024-08-05';
    // data.end_date='2024-09-14';
    let params={
      data:{
        "type": "records",
        data:data
      }
    }
    https(params).then((res)=>{
      console.log(res.data)
      that.setData({
        listData:res.data
      })
      if(that.data.listData.length==0&&that.data.type==0){
        wx.showModal({
          title: '提示',
          content: '暂无记录',
          showCancel:false,
          complete: (res) => {
            if (res.cancel) {
              wx.navigateBack({
                delta: 1
              })
            }
            if (res.confirm) {
              wx.navigateBack({
                delta: 1
              })
            }
          }
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    var that=this;
    that.setData({
      type:options.id
    })
    that.getOrder();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})