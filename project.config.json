{"compileType": "miniprogram", "libVersion": "trial", "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "ignoreUploadUnusedFiles": true}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "packOptions": {"ignore": [], "include": []}, "appid": "wxb4bc9ecd6ead5849"}