// pages/pay/index.js
var app=getApp();
import {https} from '../../utils/service'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    fullDiscount:0,// 满减
    pay_channels:[],
    waitingCheckoutSum:0,// 应付
    waitingCheckoutAlreadypaid:0,// 已支付
    waitingCheckoutRemainingPayment:0,// 剩余支付
    goodsList:[],
    xf:0,
    ky:0,
    payList:[],
    discountInfo:{}
  },
  input_price(e){
    let val=e.detail.value;
    this.setData({
      xf:val
    })
  },
  input_price2(e){
    let val=e.detail.value;
    this.setData({
      ky:val
    })
  },
  btn_submit(){
    var that=this;
    try {
      var value = wx.getStorageSync('payList');
      if (value) {
        that.setData({
          payList:value
        })
        let now_time=app.getCurrentTime2();
        that.data.payList.push({
          c_type:that.data.discountInfo.c_type,
          writeoff_amount:that.data.discountInfo.free_amount,
          writeoff_time:now_time,
          channel_type:that.data.discountInfo.channel_flag.toString()
        })
        wx.setStorageSync('payList', that.data.payList);
        let status='成功';
        let msg='满减成功';
        wx.reLaunch({
          url: 'result?status='+status+'&msg='+msg,
        })
        // Do something with return value
      }else{
        let payList=[];
        let now_time=app.getCurrentTime2();
        payList.push({
          c_type:that.data.discountInfo.c_type,
          writeoff_amount:that.data.discountInfo.free_amount,
          writeoff_time:now_time,
          channel_type:that.data.discountInfo.channel_flag.toString()
        })
        that.setData({
          payList:payList
        })
        wx.setStorageSync('payList', that.data.payList);
        let status='成功';
        let msg='满减成功';
        wx.reLaunch({
          url: 'result?status='+status+'&msg='+msg,
        })
      }
    } catch (e) {
      // Do something when catch error
    }
    // 计算剩余
    // app.calc_price();
  },
  getFree(){
    var that=this;
    let data={};
    let t_goods=[];
    that.data.goodsList.forEach((t,i)=>{
      t_goods.push({
        gcode:t.c_gcode,
        qty:t.c_nowNumber,
        amount:t.c_nowSum,
      })
    })
    data=t_goods;
    let params={
      data:{
        "type": "free",
        data:data
      }
    }
    https(params).then((res)=>{
      console.log(res)
      if(res.status=='成功'){
        if(res.data.free_amount==0){
          wx.showToast({
            title: '暂无满减',
            icon:'error'
          })
          setTimeout(() => {
            x.navigateBack({
              delta: 1
            })
          }, 1500);
        }else{
          that.setData({
            ky:res.data.free_amount,
            xf:res.data.free_amount,
            discountInfo:res.data
          })
        }
      }else{
        wx.showToast({
          title: '获取失败',
          icon:'error'
        })
        setTimeout(() => {
          x.navigateBack({
            delta: 1
          })
        }, 3000);
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    var that=this;
    try {
      var value = wx.getStorageSync('waitingCheckoutList')
      if (value) {
        that.setData({
          goodsList:value
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('waitingCheckoutSum')
      if (value) {
        that.setData({
          waitingCheckoutSum:value
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('waitingCheckoutAlreadypaid')
      if (value) {
        that.setData({
          waitingCheckoutAlreadypaid:value
        })
      }
    } catch (e) {
    }
    try {
      var value = wx.getStorageSync('waitingCheckoutRemainingPayment')
      if (value) {
        that.setData({
          waitingCheckoutRemainingPayment:value
        })
      }
    } catch (e) {
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    this.getFree();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    wx.hideHomeButton();  
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})